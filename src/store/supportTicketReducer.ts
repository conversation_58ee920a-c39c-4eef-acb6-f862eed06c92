import {
    GET_LIST_MESSAGE_SUCCESS,
    GET_LIST_MESSAGE_FAIL,
    GET_LIST_MESSAGE_PENDING,
    READ_COMMENT_SUCCESS,
    READ_COMMENT_FAIL,
    READ_COMMENT_PENDING,
    <PERSON>AR<PERSON>_MESSAGES_SUCCESS,
    <PERSON><PERSON>CH_MESSAGES_FAIL,
    SEARCH_MESSAGES_PENDING,
    NAVIGATE_SEARCH_RESULT,
    SEARCH_MESSAGE_BY_ID_SUCCESS,
    SEARCH_MESSAGE_BY_ID_FAIL,
    SEARCH_MESSAGE_BY_ID_PENDING,
    REMOVE_COMMENT_SUCCESS,
    REMOVE_COMMENT_FAIL,
    REMOVE_COMMENT_PENDING,
    <PERSON><PERSON><PERSON>_SEARCH_HIGHLIGHT,
    ATTACH_FILE_SUCCESS,
    ATTACH_FILE_FAIL,
    ATTACH_FILE_PENDING,
    UPDATE_LIST_MESSAGE
} from '../actions/supportTicketActions'
import {
    CommonActionTypes,
    IMessage,
    IMessageResponse,
    ISearchMessageResponse,
    ISearchMessageResult,
    IMessagePosition,
    initError,
    IStandardReducer
} from '../services/types'

interface IGetListMessage extends IStandardReducer {
    data: IMessage[]
    hasMore: boolean
    currentPage: number
}

interface IGetMessageData extends IStandardReducer {
    data: IMessageResponse[]
}

interface IReadComment extends IStandardReducer {
    data: boolean
}

interface ISearchMessages extends IStandardReducer {
    data: ISearchMessageResult[]
    foundPositions: IMessagePosition[]
    highlightedMessageId: number | null
    currentSearchIndex: number
}

interface IAttachFile extends IStandardReducer {
    data: any
}

export interface ISupportTicketState {
    getMessageData: IGetMessageData
    getListMessage: IGetListMessage
    readComment: IReadComment
    searchMessages: ISearchMessages
    attachFile: IAttachFile
}

const initialState: ISupportTicketState = {
    getMessageData: {
        loading: false,
        error: initError,
        data: []
    },
    getListMessage: {
        loading: false,
        error: initError,
        data: [],
        hasMore: true,
        currentPage: 0
    },
    readComment: {
        loading: false,
        error: initError,
        data: false
    },
    searchMessages: {
        loading: false,
        error: initError,
        data: [],
        foundPositions: [],
        highlightedMessageId: null,
        currentSearchIndex: 0
    },
    attachFile: {
        loading: false,
        error: initError,
        data: null
    }
}

const supportTicketReducer = (
    state: ISupportTicketState = initialState,
    action: CommonActionTypes
): ISupportTicketState => {
    switch (action.type) {
        case GET_LIST_MESSAGE_PENDING:
            return {
                ...state,
                getListMessage: {
                    ...state.getListMessage,
                    loading: true,
                    error: initError,
                    // Nếu iDisplayStart = 0, đây là request mới, reset currentPage
                    currentPage:
                        action.payload?.pageRequest?.iDisplayStart === 0
                            ? 0
                            : state.getListMessage.currentPage
                }
            }

        case GET_LIST_MESSAGE_SUCCESS:
            // Kiểm tra xem đây là request đầu tiên hay request tải thêm
            const isFirstRequest =
                action.payload?.pageRequest?.iDisplayStart === 0

            // Nếu là request đầu tiên, thay thế toàn bộ dữ liệu
            // Nếu là request tải thêm, thêm vào dữ liệu hiện có
            const currentData = isFirstRequest
                ? []
                : [...state.getListMessage.data]

            // Lọc bỏ các tin nhắn trùng lặp trước khi thêm vào danh sách
            const existingIds = new Set(
                currentData.map((msg: IMessage) => msg.id)
            )
            const newMessages =
                action.payload?.data.filter(
                    (msg: IMessage) => !existingIds.has(msg.id)
                ) || []

            const newData = [...currentData, ...newMessages]

            // Kiểm tra xem còn dữ liệu để tải không
            const recordsTotal = action.payload?.recordsTotal || 0
            const hasMore = newData.length < recordsTotal

            return {
                ...state,
                getListMessage: {
                    loading: false,
                    error: initError,
                    data: newData,
                    hasMore: hasMore,
                    currentPage: isFirstRequest
                        ? 1
                        : state.getListMessage.currentPage + 1
                },
                getMessageData: {
                    loading: false,
                    error: initError,
                    data: action.payload
                }
            }

        case GET_LIST_MESSAGE_FAIL:
            return {
                ...state,
                getListMessage: {
                    ...state.getListMessage,
                    loading: false,
                    error: action.payload
                }
            }

        case UPDATE_LIST_MESSAGE:
            const { newMessage, type } = action.payload

            const TYPE = type || 'message'
            let updateData: any[] = []

            switch (TYPE) {
                case 'message':
                    // Kiểm tra xem tin nhắn đã tồn tại trong danh sách chưa
                    const messageExists = state.getListMessage.data.some(
                        (msg: IMessage) => msg.id === newMessage.id
                    )

                    // Chỉ thêm tin nhắn mới nếu nó chưa tồn tại
                    if (!messageExists) {
                        updateData = [...state.getListMessage.data, newMessage]
                    } else {
                        console.log('Skipping duplicate message in reducer:', newMessage.id)
                        updateData = state.getListMessage.data
                    }
                    break
                case 'delete': {
                    // Kiểm tra xem tin nhắn có tồn tại trong danh sách không
                    const messageExists = state.getListMessage.data.some(
                        (msg: IMessage) => msg.id === newMessage.id
                    )

                    // Nếu tin nhắn tồn tại, cập nhật trạng thái thành DEACTIVE
                    if (messageExists) {
                        console.log('Updating message status to DEACTIVE:', newMessage.id)
                        updateData = state.getListMessage.data.map(
                            (msg: IMessage) =>
                                msg.id !== newMessage.id
                                    ? msg
                                    : { ...msg, status: 'DEACTIVE' }
                        )
                    } else {
                        // Nếu tin nhắn không tồn tại trong danh sách, có thể cần tải lại danh sách
                        console.log('Message not found in list, keeping current data:', newMessage.id)
                        updateData = state.getListMessage.data
                    }
                    break
                }
                default:
                    updateData = state.getListMessage.data
                    break
            }

            return {
                ...state,
                getListMessage: {
                    ...state.getListMessage,
                    data: updateData
                }
            }

        case READ_COMMENT_PENDING:
            return {
                ...state,
                readComment: {
                    ...state.readComment,
                    loading: true,
                    error: initError
                }
            }

        case READ_COMMENT_SUCCESS:
            return {
                ...state,
                readComment: {
                    loading: false,
                    error: initError,
                    data: action.payload?.success || false
                }
            }

        case READ_COMMENT_FAIL:
            return {
                ...state,
                readComment: {
                    ...state.readComment,
                    loading: false,
                    error: action.payload
                }
            }

        case SEARCH_MESSAGES_PENDING:
            return {
                ...state,
                searchMessages: {
                    ...state.searchMessages,
                    loading: true,
                    error: initError
                }
            }

        case SEARCH_MESSAGES_SUCCESS:
            const allFoundPositions: IMessagePosition[] = []
            if (action.payload?.data && action.payload.data.length > 0) {
                action.payload.data.forEach((result: ISearchMessageResult) => {
                    if (
                        result.arrayPositionMsgFound &&
                        result.arrayPositionMsgFound.length > 0
                    ) {
                        // Lọc bỏ tin nhắn bị thu hồi khỏi kết quả tìm kiếm
                        // Kiểm tra trong danh sách tin nhắn hiện tại
                        const filteredPositions =
                            result.arrayPositionMsgFound.filter((position) => {
                                // Tìm tin nhắn trong danh sách hiện tại
                                const message = state.getListMessage.data.find(
                                    (msg: IMessage) =>
                                        msg.id === position.messageID
                                )
                                // Chỉ giữ lại tin nhắn không bị thu hồi (không có status hoặc status khác DEACTIVE)
                                return !message || message.status !== 'DEACTIVE'
                            })

                        allFoundPositions.push(...filteredPositions)
                    }
                })
            }

            // Sắp xếp các vị trí theo thứ tự position
            allFoundPositions.sort((a, b) => a.position - b.position)

            const firstMessageId =
                allFoundPositions.length > 0
                    ? allFoundPositions[0].messageID
                    : null

            return {
                ...state,
                searchMessages: {
                    loading: false,
                    error: initError,
                    data: action.payload?.data || [],
                    foundPositions: allFoundPositions,
                    highlightedMessageId: firstMessageId,
                    currentSearchIndex: 0
                }
            }

        case SEARCH_MESSAGES_FAIL:
            return {
                ...state,
                searchMessages: {
                    ...state.searchMessages,
                    loading: false,
                    error: action.payload
                }
            }

        case NAVIGATE_SEARCH_RESULT:
            // Đảm bảo chỉ cập nhật khi có kết quả tìm kiếm
            if (state.searchMessages.foundPositions.length === 0) {
                return state
            }

            const newIndex = action.payload.currentSearchIndex
            const newHighlightedMessageId =
                state.searchMessages.foundPositions[newIndex].messageID

            return {
                ...state,
                searchMessages: {
                    ...state.searchMessages,
                    currentSearchIndex: newIndex,
                    highlightedMessageId: newHighlightedMessageId
                }
            }

        case SEARCH_MESSAGE_BY_ID_PENDING:
            return {
                ...state,
                searchMessages: {
                    ...state.searchMessages,
                    loading: true,
                    error: initError
                }
            }

        case SEARCH_MESSAGE_BY_ID_SUCCESS:
            const messagePositions: IMessagePosition[] = []
            if (action.payload?.data && action.payload.data.length > 0) {
                action.payload.data.forEach((result: ISearchMessageResult) => {
                    if (
                        result.arrayPositionMsgFound &&
                        result.arrayPositionMsgFound.length > 0
                    ) {
                        // Lọc bỏ tin nhắn bị thu hồi khỏi kết quả tìm kiếm
                        const filteredPositions =
                            result.arrayPositionMsgFound.filter((position) => {
                                // Tìm tin nhắn trong danh sách hiện tại
                                const message = state.getListMessage.data.find(
                                    (msg: IMessage) =>
                                        msg.id === position.messageID
                                )
                                // Chỉ giữ lại tin nhắn không bị thu hồi (không có status hoặc status khác DEACTIVE)
                                return !message || message.status !== 'DEACTIVE'
                            })

                        messagePositions.push(...filteredPositions)
                    }
                })
            }

            const messageId =
                messagePositions.length > 0
                    ? messagePositions[0].messageID
                    : null

            return {
                ...state,
                searchMessages: {
                    loading: false,
                    error: initError,
                    data: action.payload?.data || [],
                    foundPositions: messagePositions,
                    highlightedMessageId: messageId,
                    currentSearchIndex: 0
                }
            }

        case SEARCH_MESSAGE_BY_ID_FAIL:
            return {
                ...state,
                searchMessages: {
                    ...state.searchMessages,
                    loading: false,
                    error: action.payload
                }
            }

        case REMOVE_COMMENT_PENDING:
            return {
                ...state,
                getListMessage: {
                    ...state.getListMessage,
                    loading: true
                }
            }

        case REMOVE_COMMENT_SUCCESS:
            // Cập nhật trạng thái tin nhắn thành DEACTIVE
            const commentId = action.payload.commentId
            const updatedMessages = state.getListMessage.data.map(
                (message: IMessage) => {
                    if (message.id === commentId) {
                        return {
                            ...message,
                            status: 'DEACTIVE'
                        }
                    }
                    return message
                }
            )

            return {
                ...state,
                getListMessage: {
                    ...state.getListMessage,
                    loading: false,
                    data: updatedMessages
                }
            }

        case REMOVE_COMMENT_FAIL:
            return {
                ...state,
                getListMessage: {
                    ...state.getListMessage,
                    loading: false,
                    error: action.payload
                }
            }

        case CLEAR_SEARCH_HIGHLIGHT:
            return {
                ...state,
                searchMessages: {
                    ...state.searchMessages,
                    highlightedMessageId: null,
                    currentSearchIndex: 0,
                    foundPositions: []
                }
            }

        case ATTACH_FILE_PENDING:
            return {
                ...state,
                attachFile: {
                    ...state.attachFile,
                    loading: true,
                    error: initError
                }
            }

        case ATTACH_FILE_SUCCESS:
            return {
                ...state,
                attachFile: {
                    loading: false,
                    error: initError,
                    data: action.payload
                }
            }

        case ATTACH_FILE_FAIL:
            return {
                ...state,
                attachFile: {
                    ...state.attachFile,
                    loading: false,
                    error: action.payload
                }
            }

        default:
            return state
    }
}

export default supportTicketReducer
