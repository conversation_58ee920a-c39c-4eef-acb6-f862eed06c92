import React from 'react'
import { BotSupportTicket } from '@screens'

const StackList = [
    {
        name: 'BotSupportTicket',
        component: BotSupportTicket,
        initialParams: {},
        options: {},
    },
]

const MainNavigator = (props: any) => {
    const { MyStack } = props
    return (
        <MyStack.Navigator
            screenOptions={{ headerShown: false }}
            initialRouteName={'BotSupportTicket'}>
            {StackList.map((stack) => {
                return (
                    <MyStack.Screen
                        key={stack.name}
                        name={stack.name}
                        component={stack.component}
                        initialParams={stack.initialParams}
                        options={stack.options}
                    />
                )
            })}
        </MyStack.Navigator>
    )
}

export default MainNavigator
