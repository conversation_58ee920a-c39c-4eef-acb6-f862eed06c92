/**
 * Hàm tách ngày và giờ từ chuỗi thời gian
 * @param dateTimeString Chuỗi thời gian định dạng "DD/MM/YYYY HH:MM:SS"
 * @returns Object chứa ngày và giờ đã tách
 */
export const splitDateTime = (dateTimeString: string): { date: string; time: string } => {
    if (!dateTimeString) {
        return { date: '', time: '' }
    }
    
    // Tách chuỗi thời gian thành phần ngày và giờ
    const parts = dateTimeString.split(' ')
    
    if (parts.length === 2) {
        return {
            date: parts[0], // DD/MM/YYYY
            time: parts[1]  // HH:MM:SS
        }
    }
    
    return { date: dateTimeString, time: '' }
}

/**
 * Hàm kiểm tra xem hai chuỗi thời gian có cùng ngày không
 * @param dateTime1 Chuỗi thời gian thứ nhất
 * @param dateTime2 Chuỗi thời gian thứ hai
 * @returns true nếu cùng ngày, false nếu khác ngày
 */
export const isSameDay = (dateTime1: string, dateTime2: string): boolean => {
    if (!dateTime1 || !dateTime2) {
        return false
    }
    
    const date1 = splitDateTime(dateTime1).date
    const date2 = splitDateTime(dateTime2).date
    
    return date1 === date2
}

/**
 * Hàm lấy phần giờ từ chuỗi thời gian
 * @param dateTimeString Chuỗi thời gian định dạng "DD/MM/YYYY HH:MM:SS"
 * @returns Chuỗi giờ định dạng "HH:MM"
 */
export const getTimeFromDateTime = (dateTimeString: string): string => {
    if (!dateTimeString) {
        return ''
    }
    
    const { time } = splitDateTime(dateTimeString)
    
    // Cắt bỏ phần giây nếu có
    if (time) {
        const timeParts = time.split(':')
        if (timeParts.length >= 2) {
            return `${timeParts[0]}:${timeParts[1]}`
        }
    }
    
    return time
}
