import { useEffect, useRef, useCallback } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'

// Biến toàn cục để lưu trữ ID cơ sở cho WebSocket
let baseWsId = '6'

/**
 * <PERSON>yển đổi chuỗi UTF-8 sang dạng hex
 * @param str Chuỗi cần chuyển đổi
 * @returns Chuỗi hex
 */
const utf8ToHex = (str: string): any => {
    try {
        return Array.from(str)
            .map((c) =>
                c.charCodeAt(0) < 128
                    ? c.charCodeAt(0).toString(16)
                    : encodeURIComponent(c).replace(/\\%/g, '').toLowerCase()
            )
            .join('')
    } catch (error) {
        return error
    }
}

/**
 * Tạo ID cho WebSocket
 * @param wsId ID cơ sở
 * @returns ID mới với timestamp và chuỗi ngẫu nhiên
 */
const generateWsId = (wsId: string) => {
    const characters =
        '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    let randomString = ''
    for (let i = 0; i < 4; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        randomString += characters.charAt(randomIndex)
    }
    return wsId + '_' + new Date().getTime() + '_' + randomString
}

// Singleton để lưu trữ WebSocket instance
let wsInstance: WebSocket | null = null
let currentProfileId: number | null = null
// Biến để theo dõi trạng thái kết nối WebSocket
let wsReadyState: number = WebSocket.CLOSED

// Hàm gửi tin nhắn qua WebSocket
export const sendWebSocketMessage = (
    destination: string,
    data: any,
    payloadType: 'message' | 'delete' | 'read' | 'typing'
) => {
    // Kiểm tra nếu WebSocket không tồn tại
    if (!wsInstance) {
        console.log('WebSocket not connected')
        return false
    }

    // Cập nhật và kiểm tra trạng thái WebSocket
    wsReadyState = wsInstance.readyState
    if (wsReadyState !== WebSocket.OPEN) {
        console.log(
            'WebSocket is not in OPEN state, current state:',
            wsReadyState
        )
        return false
    }

    try {
        let message

        // Xử lý đặc biệt cho sự kiện typing
        if (payloadType === 'typing') {
            message = {
                id: generateWsId(baseWsId),
                type: 'typing',
                destination,
                payload: data // data là boolean (true hoặc false)
            }
        } else {
            // Định dạng thông thường cho các loại tin nhắn khác
            const payload = {
                data,
                payloadType
            }

            message = {
                id: generateWsId(baseWsId),
                type: 'default',
                destination,
                payload: JSON.stringify(payload)
            }
        }

        // Kiểm tra lại trạng thái WebSocket trước khi gửi
        wsReadyState = wsInstance.readyState
        if (wsReadyState === WebSocket.OPEN) {
            try {
                wsInstance.send(JSON.stringify(message))
                console.log('WebSocket message sent:', message)
                return true
            } catch (sendError) {
                console.log('Error during WebSocket send operation:', sendError)
                return false
            }
        } else {
            console.log(
                'WebSocket state changed before sending, current state:',
                wsReadyState
            )
            return false
        }
    } catch (error) {
        console.log('Error preparing WebSocket message:', error)
        return false
    }
}

// Hàm kiểm tra trạng thái kết nối WebSocket
export const isWebSocketConnected = () => {
    return wsInstance !== null
}

// Hàm lấy trạng thái chi tiết của WebSocket
export const getWebSocketStatus = () => {
    // Cập nhật trạng thái hiện tại nếu WebSocket tồn tại
    if (wsInstance) {
        wsReadyState = wsInstance.readyState
    }

    return {
        connected: wsInstance !== null,
        readyState: wsReadyState,
        readyStateText: getReadyStateText(wsReadyState),
        profileId: currentProfileId
    }
}

// Hàm chuyển đổi mã trạng thái WebSocket thành văn bản
const getReadyStateText = (state: number): string => {
    switch (state) {
        case WebSocket.CONNECTING:
            return 'CONNECTING'
        case WebSocket.OPEN:
            return 'OPEN'
        case WebSocket.CLOSING:
            return 'CLOSING'
        case WebSocket.CLOSED:
            return 'CLOSED'
        default:
            return 'UNKNOWN'
    }
}



interface UseWebSocketParams {
    profileId: number
    onMessage: (
        messageData: any,
        payloadType: 'message' | 'delete' | 'read'
    ) => void
    onTypingStatus?: (isTyping: boolean) => void
    profile?: any
}

/**
 * Custom hook để quản lý kết nối WebSocket
 * @param profileId ID của người dùng
 * @param onMessage Callback khi nhận được tin nhắn mới
 * @returns WebSocket instance và hàm gửi tin nhắn
 *
 * Lưu ý: Hook này được thiết kế để duy trì kết nối WebSocket vĩnh viễn.
 * Kết nối sẽ không bị đóng khi component unmount và sẽ tự động kết nối lại
 * khi mất kết nối. Điều này giúp đảm bảo luôn có kết nối WebSocket sẵn sàng
 * để nhận và gửi tin nhắn.
 */
const useWebSocket = ({
    profileId,
    onMessage,
    onTypingStatus,
    profile
}: UseWebSocketParams) => {
    const wsRef = useRef<WebSocket | null>(null)

    // Biến lưu trữ ID của người dùng hiện tại từ sự kiện CONNECTED
    const currentUserIdRef = useRef<string>('')

    const connectWS = useCallback(async () => {
        try {
            // Lấy token xác thực từ AsyncStorage
            const subToken =
                (await AsyncStorage.getItem('SUBTOKEN_NOTIFICATION')) || ''
            const lastCharacter = subToken.length - 1

            const withoutQuotesSubtoken = subToken.slice(1, lastCharacter)

            // Khởi tạo kết nối WebSocket
            wsRef.current = new WebSocket('wss://chat.tgdd.vn/chat', [
                '0',
                utf8ToHex(withoutQuotesSubtoken)
            ])

            // Xử lý sự kiện khi kết nối thành công
            wsRef.current.onopen = () => {
                console.log('WebSocket onopen: ', 'connected')
                // Cập nhật trạng thái WebSocket
                if (wsRef.current) {
                    wsReadyState = wsRef.current.readyState
                    wsInstance = wsRef.current
                }
            }

            // Xử lý sự kiện khi nhận được tin nhắn
            wsRef.current.onmessage = (event: any) => {
                try {
                    console.log('WebSocket onmessage: ', event.data)
                    const data = JSON.parse(event.data)
                    switch (data.type) {
                        case 'CONNECTED':
                            // Lưu ID của người dùng hiện tại từ sự kiện CONNECTED
                            if (data.id) {
                                currentUserIdRef.current = data.id
                            }

                            wsRef.current?.send(
                                JSON.stringify({
                                    id: generateWsId(data.id),
                                    type: 'subscribe',
                                    destination: '',
                                    payload: ['TICKET_SIM_SO_USER_' + profile.id]
                                })
                            )
                            break

                        case 'typing':
                            // Xử lý sự kiện typing
                            console.log('WebSocket typing event received:', data)

                            // So sánh source với ID người dùng hiện tại
                            const isFromCurrentUser = data.source === currentUserIdRef.current
                            // Chỉ thay đổi trạng thái typing khi nguồn khác với ID người dùng hiện tại
                            if (
                                !isFromCurrentUser &&
                                onTypingStatus &&
                                typeof data.payload === 'boolean'
                            ) {
                                console.log('Updating typing status from other user:', data.payload)
                                // Gọi callback với trạng thái typing (true/false)
                                onTypingStatus(data.payload)
                            }
                            break

                        case 'default':
                            const messageData = JSON.parse(data.payload)
                            onMessage(
                                messageData?.data,
                                messageData.payloadType
                            )
                            break
                        case 'ping': {
                            wsRef.current?.send(
                                JSON.stringify({
                                    type: 'pong'
                                })
                            )
                            break
                        }
                        default:
                            break
                    }
                } catch (error) {
                    console.log('firstLog WS ONmessage error ', error)
                }
            }

            // Xử lý sự kiện khi có lỗi
            wsRef.current.onerror = (event: any) => {
                console.log('firstLog onerror: ', event)
                wsRef.current?.close()
                connectWS()
            }

            // Xử lý sự kiện khi đóng kết nối
            wsRef.current.onclose = (event: any) => {
                console.log('firstLog onclose: ', event)
            }
        } catch (error) {
            console.log('WebSocket connection error:', error)
        }
    }, [profileId, onMessage, profile])

    // Khởi tạo kết nối khi component mount
    useEffect(() => {
        connectWS()

        return () => {
            console.log('firstLog unmount ')
            wsRef.current?.close()
        }
    }, [])

    // Cập nhật singleton instance
    useEffect(() => {
        if (wsRef.current) {
            wsInstance = wsRef.current
            currentProfileId = profileId
        }
    }, [wsRef.current, profileId])

    // Trả về WebSocket instance, hàm gửi tin nhắn và hàm kiểm tra trạng thái kết nối
    return {
        ws: wsRef.current,
        sendMessage: sendWebSocketMessage,
        isConnected: isWebSocketConnected
    }
}

export default useWebSocket
