import { useEffect, useRef, useCallback } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'
import moment from 'moment'

// Biến toàn cục để lưu trữ ID cơ sở cho WebSocket
let baseWsId = '6'

/**
 * Chuyển đổi chuỗi UTF-8 sang dạng hex
 * @param str Chuỗi cần chuyển đổi
 * @returns Chuỗi hex
 */
const utf8ToHex = (str: string): any => {
    try {
        return Array.from(str)
            .map((c) =>
                c.charCodeAt(0) < 128
                    ? c.charCodeAt(0).toString(16)
                    : encodeURIComponent(c).replace(/\\%/g, '').toLowerCase()
            )
            .join('')
    } catch (error) {
        return error
    }
}

/**
 * Tạo ID cho WebSocket
 * @param wsId ID cơ sở
 * @returns ID mới với timestamp và chuỗi ngẫu nhiên
 */
const generateWsId = (wsId: string) => {
    const characters =
        '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    let randomString = ''
    for (let i = 0; i < 4; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        randomString += characters.charAt(randomIndex)
    }
    return wsId + '_' + new Date().getTime() + '_' + randomString
}

// Singleton để lưu trữ WebSocket instance
let wsInstance: WebSocket | null = null
let currentProfileId: number | null = null
// Biến để theo dõi trạng thái kết nối WebSocket
let wsReadyState: number = WebSocket.CLOSED

// Hàm gửi tin nhắn qua WebSocket
export const sendWebSocketMessage = (
    destination: string,
    data: any,
    payloadType: 'message' | 'delete' | 'read' | 'typing'
) => {
    // Kiểm tra nếu WebSocket không tồn tại
    if (!wsInstance) {
        console.log('WebSocket not connected')
        return false
    }

    // Cập nhật và kiểm tra trạng thái WebSocket
    wsReadyState = wsInstance.readyState
    if (wsReadyState !== WebSocket.OPEN) {
        console.log(
            'WebSocket is not in OPEN state, current state:',
            wsReadyState
        )
        return false
    }

    try {
        let message

        // Xử lý đặc biệt cho sự kiện typing
        if (payloadType === 'typing') {
            message = {
                id: generateWsId(baseWsId),
                type: 'typing',
                destination,
                payload: data // data là boolean (true hoặc false)
            }
        } else {
            // Định dạng thông thường cho các loại tin nhắn khác
            const payload = {
                data,
                payloadType
            }

            message = {
                id: generateWsId(baseWsId),
                type: 'default',
                destination,
                payload: JSON.stringify(payload)
            }
        }

        // Kiểm tra lại trạng thái WebSocket trước khi gửi
        wsReadyState = wsInstance.readyState
        if (wsReadyState === WebSocket.OPEN) {
            try {
                wsInstance.send(JSON.stringify(message))
                console.log('WebSocket message sent:', message)
                return true
            } catch (sendError) {
                console.log('Error during WebSocket send operation:', sendError)
                return false
            }
        } else {
            console.log(
                'WebSocket state changed before sending, current state:',
                wsReadyState
            )
            return false
        }
    } catch (error) {
        console.log('Error preparing WebSocket message:', error)
        return false
    }
}

// Hàm kiểm tra trạng thái kết nối WebSocket
export const isWebSocketConnected = () => {
    return wsInstance !== null
}

// Hàm lấy trạng thái chi tiết của WebSocket
export const getWebSocketStatus = () => {
    // Cập nhật trạng thái hiện tại nếu WebSocket tồn tại
    if (wsInstance) {
        wsReadyState = wsInstance.readyState
    }

    return {
        connected: wsInstance !== null,
        readyState: wsReadyState,
        readyStateText: getReadyStateText(wsReadyState),
        profileId: currentProfileId
    }
}

// Hàm chuyển đổi mã trạng thái WebSocket thành văn bản
const getReadyStateText = (state: number): string => {
    switch (state) {
        case WebSocket.CONNECTING:
            return 'CONNECTING'
        case WebSocket.OPEN:
            return 'OPEN'
        case WebSocket.CLOSING:
            return 'CLOSING'
        case WebSocket.CLOSED:
            return 'CLOSED'
        default:
            return 'UNKNOWN'
    }
}

// Hàm kiểm tra tính hợp lệ của token
const isValidToken = (token: string | null): boolean => {
    if (!token) return false
    if (token.length < 10) return false // Token quá ngắn

    // Kiểm tra định dạng token
    // Nếu token được bọc trong dấu ngoặc kép, loại bỏ chúng
    const processedToken =
        token.startsWith('"') && token.endsWith('"')
            ? token.slice(1, token.length - 1)
            : token

    // Kiểm tra token có chứa ký tự không hợp lệ không
    const hasInvalidChars = /[^\x20-\x7E]/.test(processedToken)
    if (hasInvalidChars) {
        console.log('Token contains invalid characters')
        return false
    }

    return true
}

interface UseWebSocketParams {
    profileId: number
    onMessage: (
        messageData: any,
        payloadType: 'message' | 'delete' | 'read'
    ) => void
    onTypingStatus?: (isTyping: boolean) => void
    profile?: any
}

/**
 * Custom hook để quản lý kết nối WebSocket
 * @param profileId ID của người dùng
 * @param onMessage Callback khi nhận được tin nhắn mới
 * @returns WebSocket instance và hàm gửi tin nhắn
 *
 * Lưu ý: Hook này được thiết kế để duy trì kết nối WebSocket vĩnh viễn.
 * Kết nối sẽ không bị đóng khi component unmount và sẽ tự động kết nối lại
 * khi mất kết nối. Điều này giúp đảm bảo luôn có kết nối WebSocket sẵn sàng
 * để nhận và gửi tin nhắn.
 */
// Hằng số cho cơ chế retry
const RETRY_DELAY = 2000 // 5 giây

// Hằng số cho cơ chế heartbeat
const HEARTBEAT_INTERVAL = 10000 // 10 giây
const HEARTBEAT_TIMEOUT = 5000 // 5 giây

const useWebSocket = ({
    profileId,
    onMessage,
    onTypingStatus,
    profile
}: UseWebSocketParams) => {
    const wsRef = useRef<WebSocket | null>(null)
    // Lưu trữ ID tin nhắn đã xử lý để tránh trùng lặp
    const processedMessageIds = useRef<Set<number>>(new Set())

    // Biến theo dõi heartbeat và thời gian hoạt động
    const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
    const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const lastActivityRef = useRef<number>(Date.now())
    const connectionMonitorRef = useRef<NodeJS.Timeout | null>(null)

    // Biến lưu trữ ID của người dùng hiện tại từ sự kiện CONNECTED
    const currentUserIdRef = useRef<string>('')

    // Hàm cập nhật thời gian hoạt động cuối cùng
    const updateLastActivity = useCallback(() => {
        lastActivityRef.current = Date.now()
    }, [])

    // Hàm gửi heartbeat để giữ kết nối
    const sendHeartbeat = useCallback(() => {
        if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            try {
                console.log('Sending heartbeat ping...')
                wsRef.current.send(JSON.stringify({ type: 'ping' }))

                // Cập nhật thời gian hoạt động
                updateLastActivity()

                // Đặt timeout để kiểm tra phản hồi
                heartbeatTimeoutRef.current = setTimeout(() => {
                    console.log('Heartbeat timeout - no pong received')
                    // Nếu không nhận được phản hồi, đóng kết nối và kết nối lại
                    if (wsRef.current) {
                        console.log(
                            'Closing WebSocket due to heartbeat timeout'
                        )
                        try {
                            wsRef.current.close()
                        } catch (closeError) {
                            console.log(
                                'Error closing WebSocket after heartbeat timeout:',
                                closeError
                            )
                        }
                    }
                }, HEARTBEAT_TIMEOUT)
            } catch (error) {
                console.log('Error sending heartbeat:', error)
            }
        } else {
            console.log('Cannot send heartbeat - WebSocket not open')
            // Nếu WebSocket không mở, dừng heartbeat
            stopHeartbeat()
        }
    }, [])

    // Hàm bắt đầu heartbeat
    const startHeartbeat = useCallback(() => {
        // Dừng heartbeat hiện tại nếu có
        stopHeartbeat()

        // Bắt đầu heartbeat mới
        console.log('Starting heartbeat interval')
        heartbeatIntervalRef.current = setInterval(
            sendHeartbeat,
            HEARTBEAT_INTERVAL
        )

        // Bắt đầu theo dõi thời gian không hoạt động
        startConnectionMonitor()
    }, [sendHeartbeat])

    // Hàm dừng heartbeat
    const stopHeartbeat = useCallback(() => {
        if (heartbeatIntervalRef.current) {
            console.log('Stopping heartbeat interval')
            clearInterval(heartbeatIntervalRef.current)
            heartbeatIntervalRef.current = null
        }

        if (heartbeatTimeoutRef.current) {
            clearTimeout(heartbeatTimeoutRef.current)
            heartbeatTimeoutRef.current = null
        }

        // Dừng theo dõi thời gian không hoạt động
        stopConnectionMonitor()
    }, [])

    // Hàm bắt đầu theo dõi thời gian không hoạt động
    const startConnectionMonitor = useCallback(() => {
        // Dừng monitor hiện tại nếu có
        stopConnectionMonitor()

        // Cập nhật thời gian hoạt động
        updateLastActivity()

        // Bắt đầu monitor mới
        console.log('Starting connection monitor')
        connectionMonitorRef.current = setInterval(() => {
            // Chỉ cập nhật thời gian hoạt động, không đóng kết nối
            updateLastActivity()
        }, 10000) // Kiểm tra mỗi 10 giây
    }, [updateLastActivity])

    // Hàm dừng theo dõi thời gian không hoạt động
    const stopConnectionMonitor = useCallback(() => {
        if (connectionMonitorRef.current) {
            console.log('Stopping connection monitor')
            clearInterval(connectionMonitorRef.current)
            connectionMonitorRef.current = null
        }
    }, [])

    const connectWS = useCallback(async () => {
        try {
            // Lấy token xác thực từ AsyncStorage
            const subToken =
                (await AsyncStorage.getItem('SUBTOKEN_NOTIFICATION')) || ''
            console.log('Raw subToken:', subToken)

            // Kiểm tra token có hợp lệ không
            if (!isValidToken(subToken)) {
                console.log('Invalid subToken detected')

                // Thử lấy token từ AsyncStorage một cách chi tiết hơn
                try {
                    const keys = await AsyncStorage.getAllKeys()
                    console.log('Available AsyncStorage keys:', keys)

                    // Kiểm tra xem có key nào liên quan đến token không
                    const tokenKeys = keys.filter(
                        (key) => key.includes('TOKEN') || key.includes('AUTH')
                    )
                    if (tokenKeys.length > 0) {
                        console.log('Found potential token keys:', tokenKeys)
                        // Thử lấy giá trị của các key này
                        for (const key of tokenKeys) {
                            const value = await AsyncStorage.getItem(key)
                            console.log(
                                `Key: ${key}, Value (first 10 chars): ${
                                    value
                                        ? value.substring(0, 10) + '...'
                                        : 'null'
                                }`
                            )
                        }
                    }
                } catch (storageError) {
                    console.log('Error checking AsyncStorage:', storageError)
                }

                throw new Error('Invalid authentication token')
            }

            // Xử lý token để loại bỏ dấu ngoặc kép nếu có
            let processedToken = subToken
            if (subToken.startsWith('"') && subToken.endsWith('"')) {
                processedToken = subToken.slice(1, subToken.length - 1)
            } else {
                // Nếu không có dấu ngoặc kép, sử dụng cách xử lý cũ
                const lastCharacter = subToken.length - 1
                processedToken = subToken.slice(1, lastCharacter)
            }

            console.log('Processed token length:', processedToken.length)

            // Chuyển đổi token thành hex
            const hexToken = utf8ToHex(processedToken)
            console.log('Hex token length:', hexToken.length)

            // Chuẩn bị URL và protocol cho WebSocket
            const wsUrl = 'wss://chat.tgdd.vn/chat'
            const wsProtocols = ['0', hexToken]

            // Kiểm tra URL WebSocket
            try {
                new URL(wsUrl)
            } catch (urlError) {
                console.log('Invalid WebSocket URL:', wsUrl, urlError)
                throw new Error('Invalid WebSocket URL')
            }

            console.log('Connecting to WebSocket:', {
                url: wsUrl,
                protocols: wsProtocols,
                tokenLength: hexToken.length
            })

            // Khởi tạo kết nối WebSocket với thông tin chi tiết hơn
            wsRef.current = new WebSocket(wsUrl, wsProtocols)

            // Thêm timeout để phát hiện lỗi kết nối
            const connectionTimeout = setTimeout(() => {
                if (
                    wsRef.current &&
                    wsRef.current.readyState === WebSocket.CONNECTING
                ) {
                    console.log('WebSocket connection timeout after 10 seconds')
                    // Đóng kết nối nếu vẫn đang trong trạng thái kết nối sau 10 giây
                    try {
                        wsRef.current.close()
                    } catch (closeError) {
                        console.log(
                            'Error closing timed out connection:',
                            closeError
                        )
                    }
                }
            }, 10000) // 10 giây timeout

            // Xử lý sự kiện khi kết nối thành công
            wsRef.current.onopen = () => {
                // Xóa timeout khi kết nối thành công
                clearTimeout(connectionTimeout)

                console.log('WebSocket connected successfully')
                // Cập nhật trạng thái WebSocket
                if (wsRef.current) {
                    wsReadyState = wsRef.current.readyState
                    wsInstance = wsRef.current
                }
                console.log(
                    'WebSocket ready state:',
                    wsReadyState,
                    getReadyStateText(wsReadyState)
                )

                // Cập nhật thời gian hoạt động
                updateLastActivity()

                // Bắt đầu heartbeat để giữ kết nối
                // startHeartbeat()
            }

            // Xử lý sự kiện khi nhận được tin nhắn
            wsRef.current.onmessage = (event: WebSocketMessageEvent) => {
                try {
                    // Cập nhật thời gian hoạt động khi nhận được tin nhắn
                    updateLastActivity()

                    console.log('WebSocket message received:', event.data)
                    const data = JSON.parse(event.data)

                    switch (data.type) {
                        case 'CONNECTED':
                            // Lưu ID của người dùng hiện tại từ sự kiện CONNECTED
                            if (data.id) {
                                console.log(
                                    'Received CONNECTED event, setting current user ID:',
                                    data.id
                                )
                                currentUserIdRef.current = data.id
                            } else {
                                console.log(
                                    'Warning: CONNECTED event without ID'
                                )
                            }

                            // Đăng ký kênh khi kết nối thành công
                            // Thêm delay nhỏ để đảm bảo WebSocket đã sẵn sàng
                            setTimeout(() => {
                                try {
                                    // Kiểm tra trạng thái WebSocket trước khi gửi
                                    if (
                                        wsRef.current &&
                                        wsRef.current.readyState ===
                                            WebSocket.OPEN
                                    ) {
                                        console.log(
                                            'Sending subscribe message...'
                                        )
                                        wsRef.current.send(
                                            JSON.stringify({
                                                id: generateWsId(data.id),
                                                type: 'subscribe',
                                                destination: '',
                                                payload: [
                                                    'TICKET_SIM_SO_USER_' +
                                                        profileId
                                                ]
                                            })
                                        )
                                        console.log(
                                            'Subscribe message sent successfully'
                                        )
                                    } else {
                                        console.log(
                                            'WebSocket not ready for sending subscribe, state:',
                                            wsRef.current?.readyState
                                        )
                                    }
                                } catch (error) {
                                    console.log(
                                        'WebSocket subscribe error:',
                                        error
                                    )
                                }
                            }, 200) // Delay 200ms
                            break

                        case 'typing':
                            // Xử lý sự kiện typing
                            console.log(
                                'WebSocket typing event received:',
                                data
                            )

                            // So sánh source với ID người dùng hiện tại
                            const isFromCurrentUser =
                                data.source === currentUserIdRef.current
                            // Chỉ thay đổi trạng thái typing khi nguồn khác với ID người dùng hiện tại
                            if (
                                !isFromCurrentUser &&
                                onTypingStatus &&
                                typeof data.payload === 'boolean'
                            ) {
                                console.log(
                                    'Updating typing status from other user:',
                                    data.payload
                                )
                                // Gọi callback với trạng thái typing (true/false)
                                onTypingStatus(data.payload)
                            }
                            break

                        case 'default':
                            // Xử lý tin nhắn nhận được
                            const messageData = JSON.parse(data.payload)
                            console.log(
                                'WebSocket payload type:',
                                messageData.payloadType
                            )

                            if (messageData.payloadType === 'message') {
                                const destination = `TICKET_SIM_SO_USER_${profile?.id}`
                                // Gửi thông tin người dùng đã đọc tin nhắn
                                const readData = {
                                    userId: profile?.id,
                                    seenTime: moment(new Date()).format(
                                        'DD-MM-YYYY HH:mm:ss'
                                    ),
                                    userName: profile?.username,
                                    userImage: profile?.image,
                                    seenTimeLong: new Date().getTime(),
                                    userLastName: profile?.lastName,
                                    userFirstName: profile?.firstName
                                }
                                console.log(
                                    'WebSocket sending read comment event:',
                                    readData
                                )
                                wsRef.current?.send(
                                    JSON.stringify({
                                        id: generateWsId(baseWsId),
                                        type: 'default',
                                        destination,
                                        payload: JSON.stringify({
                                            data: readData,
                                            payloadType: 'read'
                                        })
                                    })
                                )
                            }

                            // Kiểm tra xem tin nhắn đã được xử lý chưa
                            if (messageData?.data && messageData.data.id) {
                                const messageId = messageData.data.id

                                // Đối với sự kiện xóa tin nhắn, luôn xử lý để đảm bảo tin nhắn được cập nhật
                                if (messageData.payloadType === 'delete') {
                                    console.log(
                                        'Processing delete event for message:',
                                        messageId
                                    )
                                    onMessage(
                                        messageData.data,
                                        messageData.payloadType
                                    )
                                    // Thêm ID vào danh sách đã xử lý để tránh xử lý trùng lặp
                                    processedMessageIds.current.add(messageId)
                                }
                                // Đối với các sự kiện khác, kiểm tra trùng lặp
                                else if (
                                    !processedMessageIds.current.has(messageId)
                                ) {
                                    processedMessageIds.current.add(messageId)
                                    onMessage(
                                        messageData.data,
                                        messageData.payloadType
                                    )
                                } else {
                                    console.log(
                                        'Skipping duplicate message:',
                                        messageId
                                    )
                                }
                            }
                            break

                        case 'ping':
                            // Phản hồi ping để giữ kết nối
                            try {
                                // Kiểm tra trạng thái WebSocket trước khi gửi
                                if (
                                    wsRef.current &&
                                    wsRef.current.readyState === WebSocket.OPEN
                                ) {
                                    console.log(
                                        'Received ping, sending pong message...'
                                    )
                                    wsRef.current.send(
                                        JSON.stringify({
                                            type: 'pong'
                                        })
                                    )
                                    // Cập nhật thời gian hoạt động
                                    updateLastActivity()
                                } else {
                                    console.log(
                                        'WebSocket not ready for sending pong, state:',
                                        wsRef.current?.readyState
                                    )
                                }
                            } catch (error) {
                                console.log(
                                    'Error sending pong message:',
                                    error
                                )
                            }
                            break
                    }
                } catch (error) {
                    console.log('WebSocket onmessage error:', error)
                }
            }

            // Xử lý sự kiện khi có lỗi
            wsRef.current.onerror = (event) => {
                // Xóa timeout khi có lỗi
                clearTimeout(connectionTimeout)

                // Dừng heartbeat và monitor
                stopHeartbeat()

                console.log('WebSocket error:', event)

                // Cập nhật trạng thái WebSocket
                if (wsRef.current) {
                    wsReadyState = wsRef.current.readyState
                    console.log(
                        'WebSocket error state:',
                        wsReadyState,
                        getReadyStateText(wsReadyState)
                    )
                }

                // Đóng kết nối nếu chưa đóng
                if (
                    wsRef.current &&
                    wsRef.current.readyState !== WebSocket.CLOSED
                ) {
                    try {
                        wsRef.current.close()
                    } catch (closeError) {
                        console.log('Error closing WebSocket:', closeError)
                    }
                }

                // Luôn thử kết nối lại
                console.log(`WebSocket error, attempting to reconnect...`)
                // Thử kết nối lại sau 2 giây
                setTimeout(() => {
                    connectWS()
                }, RETRY_DELAY)
            }

            // Xử lý sự kiện khi đóng kết nối
            wsRef.current.onclose = (event) => {
                // Xóa timeout khi kết nối đóng
                clearTimeout(connectionTimeout)

                // Dừng heartbeat và monitor
                stopHeartbeat()

                console.log('WebSocket closed:', event)

                // Cập nhật trạng thái WebSocket
                wsReadyState = WebSocket.CLOSED
                console.log(
                    'WebSocket close state:',
                    wsReadyState,
                    getReadyStateText(wsReadyState)
                )

                // Kiểm tra mã lỗi đóng kết nối
                if (event.code === 1006) {
                    console.log(
                        'WebSocket closed abnormally (code 1006), possible network issue or server rejected connection'
                    )
                } else if (event.code === 1002) {
                    console.log(
                        'WebSocket protocol error (code 1002), check protocol format'
                    )
                } else if (event.code === 1008) {
                    console.log(
                        'WebSocket policy violation (code 1008), likely authentication issue'
                    )
                }

                // Kiểm tra nếu đóng kết nối không phải do lỗi (code 1000 là đóng bình thường)
                if (event.code !== 1000) {
                    // Luôn thử kết nối lại
                    console.log(
                        `WebSocket closed unexpectedly, attempting to reconnect...`
                    )
                    // Thử kết nối lại sau 5 giây
                    setTimeout(() => {
                        connectWS()
                    }, RETRY_DELAY)
                } else {
                    // Đóng kết nối bình thường, cập nhật trạng thái
                    console.log('WebSocket closed normally')
                    wsInstance = null
                }
            }
        } catch (error) {
            console.log('WebSocket connection error:', error)

            // Cập nhật trạng thái WebSocket
            wsReadyState = WebSocket.CLOSED

            // Đảm bảo tham chiếu được xóa nếu có lỗi
            if (wsRef.current) {
                try {
                    wsRef.current.close()
                } catch (closeError) {
                    console.log(
                        'Error closing WebSocket after connection error:',
                        closeError
                    )
                }
                wsRef.current = null
            }

            // Luôn thử kết nối lại
            console.log(
                `WebSocket connection error, attempting to reconnect...`
            )
            // Thử kết nối lại sau 5 giây
            setTimeout(() => {
                connectWS()
            }, RETRY_DELAY)
        }
    }, [profileId, onMessage])

    // Theo dõi sự thay đổi kết nối mạng
    useEffect(() => {
        // Đăng ký lắng nghe sự kiện thay đổi kết nối mạng
        const unsubscribe = NetInfo.addEventListener((state) => {
            console.log('Network connection state changed:', state.isConnected)

            // Nếu kết nối mạng được khôi phục
            if (state.isConnected) {
                // Kiểm tra trạng thái WebSocket hiện tại
                if (
                    wsRef.current &&
                    wsRef.current.readyState !== WebSocket.OPEN
                ) {
                    console.log(
                        'Network reconnected, attempting to reconnect WebSocket'
                    )
                    // Đóng kết nối cũ nếu còn tồn tại
                    try {
                        wsRef.current.close()
                    } catch (error) {
                        console.log('Error closing existing WebSocket:', error)
                    }
                    // Kết nối lại sau 2 giây để đảm bảo mạng đã ổn định
                    setTimeout(() => {
                        connectWS()
                    }, 2000)
                }
            } else {
                // Nếu mất kết nối mạng, ghi log và dừng heartbeat
                console.log('Network disconnected, WebSocket may disconnect')
                stopHeartbeat()
            }
        })

        // Thiết lập kiểm tra kết nối định kỳ
        const connectionCheckInterval = setInterval(() => {
            if (wsRef.current && wsRef.current.readyState !== WebSocket.OPEN) {
                console.log(
                    'WebSocket not in OPEN state, attempting to reconnect...'
                )
                try {
                    wsRef.current.close()
                } catch (error) {
                    console.log('Error closing existing WebSocket:', error)
                }
                setTimeout(() => {
                    connectWS()
                }, 1000)
            }
        }, 30000) // Kiểm tra mỗi 30 giây

        // Hủy đăng ký khi component unmount
        return () => {
            unsubscribe()
            clearInterval(connectionCheckInterval)
        }
    }, [connectWS])

    // Khởi tạo kết nối khi component mount
    useEffect(() => {
        connectWS()

        // Khi component unmount, chỉ dừng heartbeat và monitor
        return () => {
            console.log('Component unmounting, stopping heartbeat and monitor')

            // Dừng heartbeat và monitor
            stopHeartbeat()

            // Không đóng kết nối WebSocket để duy trì kết nối vĩnh viễn
            // Kết nối sẽ được duy trì và sử dụng lại khi cần thiết
        }
    }, [connectWS])

    // Cập nhật singleton instance
    useEffect(() => {
        if (wsRef.current) {
            wsInstance = wsRef.current
            currentProfileId = profileId
        }
    }, [wsRef.current, profileId])

    // Trả về WebSocket instance, hàm gửi tin nhắn và hàm kiểm tra trạng thái kết nối
    return {
        ws: wsRef.current,
        sendMessage: sendWebSocketMessage,
        isConnected: isWebSocketConnected
    }
}

export default useWebSocket
