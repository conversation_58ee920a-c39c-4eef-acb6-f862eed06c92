import React from 'react'
import { View, StyleSheet } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import { IMessage } from '@types'
import { getTimeFromDateTime } from '../../common/helper/dateTimeHelper'
import VideoMessageView from './VideoMessageView'
import TruncatedText from './TruncatedText'

interface FocusedMessageViewProps {
    item: IMessage
    isCurrentUser: boolean
}

const FocusedMessageView = ({ item, isCurrentUser }: FocusedMessageViewProps) => {
    if (isCurrentUser) {
        return (
            <View
                style={[
                    styles.currentUserMessageContainer,
                    styles.highlightedMessageCurrent
                ]}>
                {item.fromComment && (
                    <View
                        style={[
                            styles.replyPreviewContainer,
                            styles.currentUserReplyContainer
                        ]}>
                        <View style={[styles.replyPreviewContent]}>
                            <View style={styles.replyPreviewContentContainer}>
                                <MyText
                                    category="bold.caption.1"
                                    text={item.fromComment.creatorName}
                                    style={styles.replyPreviewName}
                                />
                                <MyText
                                    category="regular.caption.1"
                                    text={item.fromComment.content}
                                    style={styles.replyPreviewText}
                                    numberOfLines={1}
                                />
                            </View>
                        </View>
                    </View>
                )}

                {item.contentType === 'IMAGE' ? (
                    <Image
                        //@ts-ignore
                        isLocal={false}
                        source={{ uri: JSON.parse(item.content).url }}
                        style={styles.messageImage}
                        resizeMode="cover"
                    />
                ) : item.contentType === 'VIDEO' ? (
                    <VideoMessageView
                        content={item.content}
                        isCurrentUser={true}
                    />
                ) : (
                    <TruncatedText
                        text={item.content}
                        style={styles.currentUserMessageText}
                        maxLength={300}
                        readMoreText="... xem thêm"
                        readMoreStyle={{ color: '#0066FF' }}
                    />
                )}
                <MyText
                    text={getTimeFromDateTime(item.createTime)}
                    category="caption"
                    style={styles.currentUserTimeText}
                />
            </View>
        )
    } else {
        return (
            <View
                style={[
                    styles.otherUserMessageContainer,
                    styles.highlightedMessage
                ]}>
                {item.fromComment && (
                    <View
                        style={[
                            styles.replyPreviewContainer,
                            styles.currentUserReplyContainer
                        ]}>
                        <View
                            style={[
                                styles.replyPreviewContent,
                                {
                                    backgroundColor: Colors.BG_NEUTRALS_SECONDARY
                                }
                            ]}>
                            <View style={styles.replyPreviewContentContainer}>
                                <MyText
                                    category="bold.caption.1"
                                    text={item.fromComment.creatorName}
                                    style={styles.replyPreviewName}
                                />
                                <MyText
                                    category="regular.caption.1"
                                    text={item.fromComment.content}
                                    style={styles.replyPreviewText}
                                    numberOfLines={1}
                                />
                            </View>
                        </View>
                    </View>
                )}

                {item.contentType === 'IMAGE' ? (
                    <Image
                        //@ts-ignore
                        isLocal={false}
                        source={{ uri: JSON.parse(item.content).url }}
                        style={styles.messageImage}
                        resizeMode="cover"
                    />
                ) : item.contentType === 'VIDEO' ? (
                    <VideoMessageView
                        content={item.content}
                        isCurrentUser={false}
                    />
                ) : (
                    <TruncatedText
                        text={item.content}
                        style={styles.otherUserMessageText}
                        maxLength={300}
                        readMoreText="... xem thêm"
                        readMoreStyle={{ color: '#0066FF' }}
                    />
                )}
                <MyText
                    text={getTimeFromDateTime(item.createTime)}
                    category="caption"
                    style={styles.otherUserTimeText}
                />
            </View>
        )
    }
}

const styles = StyleSheet.create({
    highlightedMessage: {
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderWidth: 2,
        borderColor: Colors.YELLOW_500,
        zIndex: 10,
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.5,
        shadowRadius: 5,
        opacity: 1
    },
    highlightedMessageCurrent: {
        backgroundColor: Colors.BRAND_950,
        borderWidth: 2,
        borderColor: Colors.YELLOW_500,
        zIndex: 10,
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.5,
        shadowRadius: 5,
        opacity: 1
    },
    replyPreviewContainer: {
        flexDirection: 'row',
        marginBottom: Mixins.scale(6),
        width: '100%',
        borderRadius: Mixins.scale(4)
    },
    currentUserReplyContainer: {
        backgroundColor: 'rgba(255,255,255,0.1)'
    },
    otherUserReplyContainer: {
        backgroundColor: 'rgba(0,0,0,0.03)'
    },
    replyPreviewContent: {
        flex: 1,
        flexDirection: 'row',
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        paddingVertical: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(16),
        borderRadius: Mixins.scale(12),
        borderTopLeftRadius: Mixins.scale(4),
        borderWidth: 1,
        borderColor: Colors.BORDER_PRIMARY,
        gap: Mixins.scale(6)
    },
    replyPreviewContentContainer: {
        flex: 1,
        gap: Mixins.scale(6)
    },
    replyPreviewName: {
        color: Colors.TEXT_PRIMARY
    },
    replyPreviewText: {
        color: Colors.TEXT_PRIMARY
    },
    currentUserMessageContainer: {
        width: '100%',
        paddingVertical: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(16),
        backgroundColor: Colors.BRAND_950,
        borderWidth: 1,
        borderColor: Colors.BRAND_900,
        borderRadius: Mixins.scale(12),
        gap: Mixins.scale(2)
    },
    otherUserMessageContainer: {
        width: '100%',
        paddingVertical: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(16),
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderWidth: 1,
        borderColor: Colors.BORDER_PRIMARY,
        borderRadius: Mixins.scale(12),
        borderTopLeftRadius: Mixins.scale(4),
        gap: Mixins.scale(2)
    },
    currentUserMessageText: {
        color: Colors.TEXT_PRIMARY
    },
    currentUserTimeText: {
        color: Colors.TEXT_DISABLE
    },
    otherUserMessageText: {
        color: Colors.TEXT_PRIMARY
    },
    otherUserTimeText: {
        color: Colors.TEXT_DISABLE
    },
    messageImage: {
        width: '100%',
        height: Mixins.scale(200),
        borderRadius: Mixins.scale(8),
        marginBottom: Mixins.scale(4)
    }
})

export default FocusedMessageView
