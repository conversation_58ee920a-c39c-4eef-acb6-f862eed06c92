import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Modal, Pressable, Platform, Linking, ActivityIndicator, Dimensions, StatusBar } from 'react-native';
import { Colors, Mixins, MyText } from '@react-native-xwork';
import { Image } from '@mwg-kits/components';
import IMAGES from '../../assets/images';
import { getTimeFromDateTime } from '../../common/helper/dateTimeHelper';
import { useSelector } from 'react-redux';
import { RootReducerType } from '../../store/reducer';

interface VideoMessageViewProps {
    content: string;
    isCurrentUser: boolean;
}

const VideoMessageView = ({ content, isCurrentUser }: VideoMessageViewProps) => {
    const [modalVisible, setModalVisible] = useState(false);
    const [videoError, setVideoError] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);
    const { VideoPlayer } = useSelector((state: RootReducerType) => state.commonReducer);

    let videoData;
    try {
        videoData = JSON.parse(content);
    } catch (error) {
        console.error('Error parsing video data:', error);
        return (
            <View style={styles.errorContainer}>
                <MyText
                    text="Không thể hiển thị video"
                    category="body.2"
                    style={isCurrentUser ? styles.currentUserText : styles.otherUserText}
                />
            </View>
        );
    }

    const { url, downloadUrl, fileName, posterImage } = videoData;

    const handleOpenVideo = () => {
        setModalVisible(true);
        setVideoError(false);
        setIsLoading(true);

        // Thiết lập timeout để hiển thị lỗi nếu video không tải được sau 10 giây
        const timeout = setTimeout(() => {
            if (isLoading) {
                setVideoError(true);
                setIsLoading(false);
            }
        }, 10000);

        setLoadingTimeout(timeout);
    };

    // Xóa timeout khi component unmount hoặc modal đóng
    useEffect(() => {
        return () => {
            if (loadingTimeout) {
                clearTimeout(loadingTimeout);
            }
        };
    }, [loadingTimeout]);

    // Xóa timeout khi modal đóng
    useEffect(() => {
        if (!modalVisible && loadingTimeout) {
            clearTimeout(loadingTimeout);
            setLoadingTimeout(null);
        }
    }, [modalVisible, loadingTimeout]);

    // Ẩn thanh trạng thái khi mở modal video
    useEffect(() => {
        if (modalVisible) {
            StatusBar.setHidden(true);
        } else {
            StatusBar.setHidden(false);
        }
    }, [modalVisible]);

    return (
        <>
            <TouchableOpacity
                style={[
                    styles.container,
                    isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer
                ]}
                onPress={handleOpenVideo}
                activeOpacity={0.7}
            >
                <View style={styles.messageContainer}>
                    <View style={styles.videoPreviewContainer}>
                        {posterImage ? (
                            <Image
                                //@ts-ignore
                                isLocal={false}
                                source={{ uri: posterImage }}
                                style={styles.messageImage}
                                resizeMode="cover"
                            />
                        ) : (
                            <View style={styles.placeholderContainer}>
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_file_generic}
                                    style={styles.placeholderIcon}
                                    resizeMode="contain"
                                />
                            </View>
                        )}
                        <View style={styles.playIconContainer}>
                            <View style={styles.playCircle}>
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_play}
                                    style={styles.playIcon}
                                    resizeMode="contain"
                                />
                            </View>
                        </View>
                    </View>
                    <MyText
                        text={getTimeFromDateTime(fileName) || ""}
                        category="caption"
                        style={isCurrentUser ? styles.currentUserTimeText : styles.otherUserTimeText}
                    />
                </View>
            </TouchableOpacity>

            {/* Video Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setModalVisible(false)}
                statusBarTranslucent={true}
                presentationStyle="overFullScreen"
            >
                <Pressable
                    style={styles.modalOverlay}
                    // onPress={() => setModalVisible(false)}
                >
                    <View style={styles.videoModalContainer}>
                        <View style={styles.videoModalHeader}>
                            <TouchableOpacity
                                onPress={() => setModalVisible(false)}
                                style={styles.closeButton}
                            >
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_close}
                                    style={styles.closeIcon}
                                />
                            </TouchableOpacity>
                        </View>
                        <View style={styles.videoPlayerContainer}>
                            {VideoPlayer && (
                                <View style={{ flex: 1 }}>
                                    {videoError ? (
                                        <View style={styles.errorContentContainer}>
                                            <MyText
                                                text={Platform.OS === 'ios' ?
                                                    "Định dạng video này không được hỗ trợ trên iOS" :
                                                    "Định dạng video này không được hỗ trợ"}
                                                category="body.2"
                                                style={{ color: Colors.TEXT_DISABLE, textAlign: 'center', marginBottom: Mixins.scale(16) }}
                                            />
                                            <TouchableOpacity
                                                style={styles.downloadButton}
                                                onPress={() => {
                                                    setModalVisible(false);
                                                    // Mở URL để tải video
                                                    Linking.openURL(url);
                                                }}
                                            >
                                                <MyText
                                                    text="Tải video"
                                                    category="body.2"
                                                    style={{ color: Colors.TEXT_LIGHT }}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                    ) : (
                                        <>
                                            <VideoPlayer
                                                source={{ uri: url }}
                                                style={styles.videoPlayer}
                                                resizeMode="contain"
                                                disableBack
                                                disableVolume={true}
                                                disableFullscreen={true}
                                                toggleResizeModeOnFullscreen={false}
                                                showOnStart={true}
                                                controlTimeout={3000}
                                                tapAnywhereToPause={true}
                                                onLoadStart={() => {
                                                    setIsLoading(true);
                                                    setVideoError(false);
                                                }}
                                                onLoad={() => {
                                                    setIsLoading(false);
                                                    // Xóa timeout khi video tải thành công
                                                    if (loadingTimeout) {
                                                        clearTimeout(loadingTimeout);
                                                        setLoadingTimeout(null);
                                                    }
                                                }}
                                                onError={(error: any) => {
                                                    console.error('Video playback error:', error);
                                                    // Ghi log chi tiết hơn về lỗi
                                                    if (error && error.error) {
                                                        console.error('Error details:', {
                                                            code: error.error.code,
                                                            domain: error.error.domain,
                                                            what: error.what,
                                                            extra: error.error.localizedDescription || error.error.localizedFailureReason
                                                        });
                                                    }

                                                    // Kiểm tra lỗi cụ thể cho iOS
                                                    const isFormatError =
                                                        (Platform.OS === 'ios' && error?.error?.domain === 'AVFoundationErrorDomain') ||
                                                        (Platform.OS === 'android' && error?.what === 1);

                                                    // Nếu là lỗi định dạng, hiển thị thông báo phù hợp
                                                    console.log('Is format error:', isFormatError);

                                                    setVideoError(true);
                                                    setIsLoading(false);

                                                    // Xóa timeout khi video gặp lỗi
                                                    if (loadingTimeout) {
                                                        clearTimeout(loadingTimeout);
                                                        setLoadingTimeout(null);
                                                    }
                                                }}
                                            />

                                            {isLoading && (
                                                <View style={styles.loadingContainer}>
                                                    <ActivityIndicator size="large" color={Colors.TEXT_PRIMARY} />
                                                </View>
                                            )}
                                        </>
                                    )}
                                </View>
                            )}
                        </View>
                    </View>
                </Pressable>
            </Modal>
        </>
    );
};



const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const styles = StyleSheet.create({
    messageImage: {
        width: '100%',
        height: Mixins.scale(200),
        borderRadius: Mixins.scale(8),
        marginBottom: Mixins.scale(4)
    },
    container: {
        width: '100%',
        marginBottom: Mixins.scale(4),
    },
    currentUserContainer: {
        alignItems: 'flex-end',
    },
    otherUserContainer: {
        alignItems: 'flex-start',
    },
    messageContainer: {
        overflow: 'hidden',
        width: '100%',
    },
    videoPreviewContainer: {
        borderRadius: Mixins.scale(8),
        overflow: 'hidden',
        position: 'relative',
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        marginBottom: Mixins.scale(4),
    },
    videoThumbnail: {
        width: '100%',
        height: '100%',
        borderRadius: Mixins.scale(8),
    },
    placeholderContainer: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY
    },
    placeholderIcon: {
        width: Mixins.scale(48),
        height: Mixins.scale(48),
        tintColor: Colors.TEXT_DISABLE
    },
    playIconContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    playCircle: {
        width: Mixins.scale(50),
        height: Mixins.scale(50),
        borderRadius: Mixins.scale(25),
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    playIcon: {
        width: Mixins.scale(20),
        height: Mixins.scale(20),
        tintColor: '#000',
    },
    timeText: {
        marginTop: Mixins.scale(4),
        alignSelf: 'flex-end',
    },
    currentUserTimeText: {
        color: Colors.TEXT_DISABLE,
    },
    otherUserTimeText: {
        color: Colors.TEXT_DISABLE,
    },
    errorContainer: {
        padding: Mixins.scale(16),
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        borderRadius: Mixins.scale(8),
        marginBottom: Mixins.scale(4)
    },
    currentUserText: {
        color: Colors.TEXT_PRIMARY
    },
    otherUserText: {
        color: Colors.TEXT_PRIMARY
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    videoModalContainer: {
        width: SCREEN_WIDTH,
        height: SCREEN_HEIGHT,
        backgroundColor: 'black',
    },
    videoModalHeader: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? Mixins.scale(40) : Mixins.scale(50),
        right: 0,
        left: 0,
        zIndex: 10,
        height: Mixins.scale(50),
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        paddingHorizontal: Mixins.scale(16),
    },
    closeButton: {
        padding: Mixins.scale(8),
    },
    closeIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        tintColor: 'white',
    },
    videoPlayerContainer: {
        flex: 1,
        backgroundColor: '#000',
        justifyContent: 'center',
        alignItems: 'center',
        paddingBottom: Mixins.scale(50), // Thêm padding để không che các nút điều khiển
    },
    videoPlayer: {
        width: SCREEN_WIDTH,
        height: SCREEN_HEIGHT - Mixins.scale(100),
        backgroundColor: '#000',
    },
    videoErrorContainer: {
        position: 'absolute',
        padding: Mixins.scale(16),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        borderRadius: Mixins.scale(8),
    },
    errorContentContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: Mixins.scale(16),
    },
    downloadButton: {
        marginTop: Mixins.scale(12),
        paddingVertical: Mixins.scale(10),
        paddingHorizontal: Mixins.scale(20),
        borderRadius: Mixins.scale(4),
        backgroundColor: '#0066CC',
    },
    loadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    }
});

export default VideoMessageView;
