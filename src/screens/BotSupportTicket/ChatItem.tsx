import React, { memo, useRef, useState, useMemo, useEffect } from 'react'
import {
    View,
    StyleSheet,
    Animated,
    TouchableOpacity,
    Modal,
    Pressable,
    Dimensions
} from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import { IFromComment, IMessage } from '@types'
import { BASE_AVATAR_URL } from '../../api'
import { PanGestureHandler, State } from 'react-native-gesture-handler'
import IMAGES from '../../assets/images'
import FocusedMessageView from './FocusedMessageView'
import FileMessageView from './FileMessageView'
import VideoMessageView from './VideoMessageView'
import ImageMessageView from './ImageMessageView'
import TruncatedText from './TruncatedText'
import HighlightedText from './HighlightedText'
import { getTimeFromDateTime } from '../../common/helper/dateTimeHelper'

// Hàm cắt ngắn tên file nếu quá dài
const getTruncatedFileName = (fileName: string) => {
    if (!fileName) return '';
    if (fileName.length <= 20) return fileName;

    // Lấy phần mở rộng của file
    const lastDotIndex = fileName.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : '';

    // Cắt ngắn tên file và giữ lại phần mở rộng
    const nameWithoutExtension = lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
    return nameWithoutExtension.substring(0, 16) + '...' + extension;
};

// Hàm lấy icon dựa vào loại file
const getFileIcon = (contentType: string) => {
    switch (contentType) {
        case 'PDF':
            return IMAGES.ic_file_pdf;
        case 'EXCEL':
            return IMAGES.ic_file_excel;
        case 'WORD':
            return IMAGES.ic_file_word;
        case 'POWERPOINT':
            return IMAGES.ic_file_powerpoint;
        default:
            return IMAGES.ic_file_generic;
    }
};

interface ChatItemProps {
    item: IMessage
    isCurrentUser: boolean
    onReply?: (message: IFromComment) => void
    isHighlighted?: boolean
    searchText?: string
    onPressReplyPreview?: (commentID: number) => void
    onCopyMessage?: (content: string) => void
    onDeleteMessage?: (messageId: number) => void
}

const ChatItem = memo(
    ({
        item,
        isCurrentUser,
        onReply,
        isHighlighted = false,
        searchText = '',
        onPressReplyPreview,
        onCopyMessage,
        onDeleteMessage
    }: ChatItemProps) => {
        const translateX = useRef(new Animated.Value(0)).current
        const [showMenu, setShowMenu] = useState(false)
        const [menuPosition, setMenuPosition] = useState({
            x: 0,
            y: 0,
            width: 0,
            height: 0
        })
        const [menuInfo, setMenuInfo] = useState({
            needsAdjustment: false,
            menuHeight: 0
        })
        const [messageOffset, setMessageOffset] = useState(0)
        const viewRef = useRef<View>(null)

        // Xử lý việc đẩy tin nhắn lên trên khi menu không đủ chỗ hiển thị
        useEffect(() => {
            if (showMenu && menuInfo.needsAdjustment) {
                // Tính toán khoảng cách cần đẩy tin nhắn lên
                const offset = menuInfo.menuHeight + Mixins.scale(20) // Thêm padding
                setMessageOffset(-offset)
            } else {
                setMessageOffset(0)
            }
        }, [showMenu, menuInfo])

        const renderMessageContent = useMemo(() => {
            return (messageItem: IMessage, isCurrentUserMessage: boolean) => {
                if (messageItem.status === 'DEACTIVE') {
                    return (
                        <MyText
                            text="Tin nhắn đã bị thu hồi"
                            category="body.2"
                            style={{
                                ...(isCurrentUserMessage ? styles.currentUserMessageText : styles.otherUserMessageText),
                                fontStyle: 'italic',
                                color: Colors.TEXT_DISABLE
                            }}
                        />
                    )
                }

                switch (messageItem.contentType) {
                    case 'IMAGE':
                        return (
                            <ImageMessageView
                                content={messageItem.content}
                                isCurrentUser={isCurrentUserMessage}
                            />
                        )
                    case 'VIDEO':
                        return (
                            <VideoMessageView
                                content={messageItem.content}
                                isCurrentUser={isCurrentUserMessage}
                            />
                        )
                    default:
                        if (['PDF', 'EXCEL', 'WORD', 'POWERPOINT', 'OTHER_FILE'].includes(messageItem.contentType)) {
                            return (
                                <FileMessageView
                                    content={messageItem.content}
                                    contentType={messageItem.contentType}
                                    isCurrentUser={isCurrentUserMessage}
                                />
                            )
                        } else if (searchText && isHighlighted) {
                            return (
                                <LocalHighlightedText
                                    text={messageItem.content}
                                    searchText={searchText}
                                    style={isCurrentUserMessage ? styles.currentUserMessageText : styles.otherUserMessageText}
                                />
                            )
                        } else {
                            return (
                                <TruncatedText
                                    text={messageItem.content}
                                    style={isCurrentUserMessage ? styles.currentUserMessageText : styles.otherUserMessageText}
                                    maxLength={300}
                                    readMoreText="... xem thêm"
                                    readMoreStyle={{ color: '#0066FF' }}
                                />
                            )
                        }
                }
            }
        }, [searchText, isHighlighted])

        const createReplyMessage = () => {
            if (onReply) {
                const fromMessage: IFromComment = {
                    commentID: item.id,
                    creatorID: item.creatorID,
                    creatorUserName: item.creatorUsername,
                    creatorName: item.creatorName,
                    creatorAvatar: item.creatorAvatar,
                    content: item.content,
                    contentType: item.contentType,
                    pageIndex: 0,
                    positionMsg: -1
                }
                onReply(fromMessage)
            }
        }

        const handleLongPress = () => {
            if (viewRef.current) {
                viewRef.current.measure(
                    (_, __, width, height, pageX, pageY) => {
                        setMenuPosition({
                            x: pageX,
                            y: pageY,
                            width: width,
                            height: height
                        })

                        // Lấy thông tin về vị trí menu từ ChatItemMenu
                        const screenHeight = Dimensions.get('window').height
                        const menuHeight = Mixins.scale(150)
                        const safeAreaBottom = screenHeight - Mixins.scale(50)
                        const defaultPositionY = pageY + height + Mixins.scale(12)

                        // Kiểm tra xem menu có vượt quá safe area không
                        const needsAdjustment = defaultPositionY + menuHeight > safeAreaBottom

                        // Cập nhật thông tin menu
                        setMenuInfo({
                            needsAdjustment,
                            menuHeight
                        })

                        setShowMenu(true)
                    }
                )
            }
        }

        const handleCopyMessage = () => {
            if (onCopyMessage) {
                onCopyMessage(item.content)
            }
            setShowMenu(false)
        }

        const handleDeleteMessage = () => {
            if (onDeleteMessage) {
                onDeleteMessage(item.id)
            }
            setShowMenu(false)
        }

        const handleCloseMenu = () => {
            setShowMenu(false)
        }

        const onGestureEvent = item.status === 'DEACTIVE'
            ? () => {} // Nếu tin nhắn đã bị thu hồi, không xử lý sự kiện vuốt
            : Animated.event(
                [{ nativeEvent: { translationX: translateX } }],
                { useNativeDriver: true }
            )

        const onHandlerStateChange = (event: any) => {
            // Nếu tin nhắn đã bị thu hồi, không cho phép vuốt để trả lời
            if (item.status === 'DEACTIVE') {
                return;
            }

            // Khi bắt đầu gesture, reset giá trị translateX
            if (event.nativeEvent.state === State.BEGAN) {
                translateX.setValue(0)
            }

            // When gesture ends (ACTIVE -> END)
            if (event.nativeEvent.oldState === State.ACTIVE) {
                // Always spring back to original position
                Animated.spring(translateX, {
                    toValue: 0,
                    useNativeDriver: true,
                    bounciness: 10,
                    speed: 12 // Faster animation for smoother feel
                }).start()

                const { translationX } = event.nativeEvent
                const swipeThreshold = 80

                // Only trigger reply when swipe exceeds threshold
                if (
                    (isCurrentUser && translationX < -swipeThreshold) ||
                    (!isCurrentUser && translationX > swipeThreshold)
                ) {
                    createReplyMessage()
                }
            }

            // Khi gesture bị hủy, đảm bảo reset về vị trí ban đầu
            if (event.nativeEvent.oldState === State.CANCELLED) {
                Animated.spring(translateX, {
                    toValue: 0,
                    useNativeDriver: true,
                    bounciness: 10,
                    speed: 12
                }).start()
            }
        }

        if (isCurrentUser) {
            return (
                <>
                    <Modal
                        visible={showMenu}
                        transparent={true}
                        animationType="fade"
                        onRequestClose={handleCloseMenu}>
                        <Pressable
                            style={styles.modalOverlay}
                            onPress={handleCloseMenu}>
                            <View
                                style={[
                                    styles.focusedMessageContainer,
                                    {
                                        position: 'absolute',
                                        top: menuPosition.y + messageOffset, // Thêm messageOffset để đẩy tin nhắn lên trên
                                        right:
                                            Dimensions.get('window').width -
                                            menuPosition.x -
                                            menuPosition.width,
                                        width: menuPosition.width
                                    }
                                ]}>
                                <FocusedMessageView
                                    item={item}
                                    isCurrentUser={isCurrentUser}
                                />
                            </View>

                            {/* Display menu */}
                            <View
                                style={[
                                    styles.modalContainer,
                                    {
                                        top: menuPosition.y + menuPosition.height + Mixins.scale(12) + messageOffset, // Hiển thị menu bên dưới tin nhắn và áp dụng offset
                                        left: menuPosition.x + menuPosition.width / 2 - Mixins.scale(100) // Căn giữa menu với tin nhắn
                                    }
                                ]}>
                                <TouchableOpacity
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        paddingVertical: Mixins.scale(12),
                                        paddingHorizontal: Mixins.scale(16)
                                    }}
                                    onPress={() => {
                                        createReplyMessage()
                                        handleCloseMenu()
                                    }}
                                    activeOpacity={0.7}>
                                    <Image
                                        //@ts-ignore
                                        isLocal
                                        source={IMAGES.ic_reply}
                                        style={{
                                            width: Mixins.scale(20),
                                            height: Mixins.scale(20),
                                            marginRight: Mixins.scale(12),
                                            tintColor: Colors.TEXT_PRIMARY
                                        }}
                                    />
                                    <MyText
                                        category="body.2"
                                        text="Trả lời"
                                        style={{ color: Colors.TEXT_PRIMARY }}
                                    />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        paddingVertical: Mixins.scale(12),
                                        paddingHorizontal: Mixins.scale(16)
                                    }}
                                    onPress={handleCopyMessage}
                                    activeOpacity={0.7}>
                                    <Image
                                        //@ts-ignore
                                        isLocal
                                        source={IMAGES.ic_copy}
                                        style={{
                                            width: Mixins.scale(20),
                                            height: Mixins.scale(20),
                                            marginRight: Mixins.scale(12),
                                            tintColor: Colors.TEXT_PRIMARY
                                        }}
                                    />
                                    <MyText
                                        category="body.2"
                                        text="Copy tin nhắn"
                                        style={{ color: Colors.TEXT_PRIMARY }}
                                    />
                                </TouchableOpacity>

                                {/* Đóng không cho xóa tin nhắn bên phía khách hàng */}
                                {/* {isCurrentUser && (
                                    <TouchableOpacity
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            paddingVertical: Mixins.scale(12),
                                            paddingHorizontal: Mixins.scale(16)
                                        }}
                                        onPress={handleDeleteMessage}
                                        activeOpacity={0.7}>
                                        <Image
                                            //@ts-ignore
                                            isLocal
                                            source={IMAGES.ic_trash}
                                            style={{
                                                width: Mixins.scale(20),
                                                height: Mixins.scale(20),
                                                marginRight: Mixins.scale(12),
                                                tintColor: Colors.RED_500
                                            }}
                                        />
                                        <MyText
                                            category="body.2"
                                            text="Thu hồi tin nhắn"
                                            style={{ color: Colors.RED_500 }}
                                        />
                                    </TouchableOpacity>
                                )} */}
                            </View>
                        </Pressable>
                    </Modal>

                    <View style={styles.currentUserContainer}>
                        <PanGestureHandler
                            activeOffsetX={[-15, 15]} // Activate after moving 15px horizontally
                            activeOffsetY={[-50, 50]} // Require more vertical movement to activate
                            onGestureEvent={onGestureEvent}
                            onHandlerStateChange={onHandlerStateChange}
                            shouldCancelWhenOutside={true}
                            maxPointers={1} // Only allow one finger
                            avgTouches={false} // Don't average touches
                            enableTrackpadTwoFingerGesture={false}>
                            <Animated.View
                                ref={viewRef}
                                style={[
                                    styles.currentUserMessageContainer,
                                    { transform: [{ translateX }] },
                                    isHighlighted &&
                                        styles.highlightedMessageCurrent,
                                    showMenu && styles.highlightedMessageCurrent
                                ]}>
                                <TouchableOpacity
                                    disabled={item.status === 'DEACTIVE'}
                                    activeOpacity={1}
                                    onLongPress={handleLongPress}
                                    style={{ flex: 1 }}>
                                    <Animated.View
                                        style={[
                                            styles.replyIconContainer,
                                            {
                                                right: Mixins.scale(-30),
                                                opacity: translateX.interpolate(
                                                    {
                                                        inputRange: [-100, -20],
                                                        outputRange: [1, 0],
                                                        extrapolate: 'clamp'
                                                    }
                                                )
                                            }
                                        ]}>
                                        <Image
                                            //@ts-ignore
                                            isLocal
                                            source={IMAGES.ic_reply}
                                            style={styles.replyIcon}
                                        />
                                    </Animated.View>

                                    {item.fromComment && (
                                        <TouchableOpacity
                                            activeOpacity={0.8}
                                            onPress={() => {
                                                if (
                                                    onPressReplyPreview &&
                                                    item.fromComment
                                                ) {
                                                    onPressReplyPreview(
                                                        item.fromComment
                                                            .commentID
                                                    )
                                                }
                                            }}
                                            style={[
                                                styles.replyPreviewContainer,
                                                styles.currentUserReplyContainer
                                            ]}>
                                            <View
                                                style={[
                                                    styles.replyPreviewContent
                                                ]}>
                                                <Image
                                                    //@ts-ignore
                                                    isLocal
                                                    source={{
                                                        uri:
                                                            BASE_AVATAR_URL +
                                                            item.fromComment
                                                                .creatorAvatar
                                                    }}
                                                    style={
                                                        styles.replyPreviewIcon
                                                    }
                                                />
                                                <View
                                                    style={
                                                        styles.replyPreviewContentContainer
                                                    }>
                                                    <MyText
                                                        category="bold.caption.1"
                                                        text={
                                                            item.fromComment
                                                                .creatorName
                                                        }
                                                        style={
                                                            styles.replyPreviewName
                                                        }
                                                    />
                                                    {item.fromComment.contentType === 'IMAGE' ? (
                                                        <View style={styles.replyImagePreviewContainer}>
                                                            <Image
                                                                //@ts-ignore
                                                                isLocal={false}
                                                                source={{ uri: JSON.parse(item.fromComment.content).url }}
                                                                style={styles.replyImageThumbnail}
                                                                resizeMode="cover"
                                                            />
                                                            <MyText
                                                                category="regular.caption.1"
                                                                text={'Hình ảnh'}
                                                                style={styles.replyPreviewText}
                                                                numberOfLines={1}
                                                            />
                                                        </View>
                                                    ) : ['PDF', 'EXCEL', 'WORD', 'POWERPOINT', 'OTHER_FILE'].includes(item.fromComment.contentType) ? (
                                                        <View style={styles.replyImagePreviewContainer}>
                                                            <Image
                                                                //@ts-ignore
                                                                isLocal
                                                                source={getFileIcon(item.fromComment.contentType)}
                                                                style={styles.replyImageThumbnail}
                                                                resizeMode="contain"
                                                            />
                                                            <MyText
                                                                category="regular.caption.1"
                                                                text={getTruncatedFileName(JSON.parse(item.fromComment.content).fileName)}
                                                                style={styles.replyPreviewText}
                                                                numberOfLines={1}
                                                            />
                                                        </View>
                                                    ) : (
                                                        <MyText
                                                            category="regular.caption.1"
                                                            text={item.fromComment.content}
                                                            style={styles.replyPreviewText}
                                                            numberOfLines={1}
                                                        />
                                                    )}
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    )}

                                    {renderMessageContent(item, true)}
                                    <MyText
                                        text={getTimeFromDateTime(item.createTime)}
                                        category="caption"
                                        style={styles.currentUserTimeText}
                                    />
                                </TouchableOpacity>
                            </Animated.View>
                        </PanGestureHandler>
                    </View>
                </>
            )
        } else {
            return (
                <>
                    <Modal
                        visible={showMenu}
                        transparent={true}
                        animationType="fade"
                        onRequestClose={handleCloseMenu}>
                        <Pressable
                            style={styles.modalOverlay}
                            onPress={handleCloseMenu}>
                            <View
                                style={[
                                    styles.focusedMessageContainer,
                                    {
                                        position: 'absolute',
                                        top: menuPosition.y + messageOffset, // Thêm messageOffset để đẩy tin nhắn lên trên
                                        left: menuPosition.x,
                                        width: menuPosition.width
                                    }
                                ]}>
                                <FocusedMessageView
                                    item={item}
                                    isCurrentUser={isCurrentUser}
                                />
                            </View>

                            {/* Display menu */}
                            <View
                                style={[
                                    styles.modalContainer,
                                    {
                                        top: menuPosition.y + menuPosition.height + Mixins.scale(12) + messageOffset, // Hiển thị menu bên dưới tin nhắn và áp dụng offset
                                        left: menuPosition.x + menuPosition.width / 2 - Mixins.scale(100) // Căn giữa menu với tin nhắn
                                    }
                                ]}>
                                <TouchableOpacity
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        paddingVertical: Mixins.scale(12),
                                        paddingHorizontal: Mixins.scale(16)
                                    }}
                                    onPress={() => {
                                        createReplyMessage()
                                        handleCloseMenu()
                                    }}
                                    activeOpacity={0.7}>
                                    <Image
                                        //@ts-ignore
                                        isLocal
                                        source={IMAGES.ic_reply}
                                        style={{
                                            width: Mixins.scale(20),
                                            height: Mixins.scale(20),
                                            marginRight: Mixins.scale(12),
                                            tintColor: Colors.TEXT_PRIMARY
                                        }}
                                    />
                                    <MyText
                                        category="body.2"
                                        text="Trả lời"
                                        style={{ color: Colors.TEXT_PRIMARY }}
                                    />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        paddingVertical: Mixins.scale(12),
                                        paddingHorizontal: Mixins.scale(16)
                                    }}
                                    onPress={handleCopyMessage}
                                    activeOpacity={0.7}>
                                    <Image
                                        //@ts-ignore
                                        isLocal
                                        source={IMAGES.ic_copy}
                                        style={{
                                            width: Mixins.scale(20),
                                            height: Mixins.scale(20),
                                            marginRight: Mixins.scale(12),
                                            tintColor: Colors.TEXT_PRIMARY
                                        }}
                                    />
                                    <MyText
                                        category="body.2"
                                        text="Copy tin nhắn"
                                        style={{ color: Colors.TEXT_PRIMARY }}
                                    />
                                </TouchableOpacity>
                                {/* Đóng không cho xóa tin nhắn bên phía khách hàng */}
                                {/* {isCurrentUser && (
                                    <TouchableOpacity
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            paddingVertical: Mixins.scale(12),
                                            paddingHorizontal: Mixins.scale(16)
                                        }}
                                        onPress={handleDeleteMessage}
                                        activeOpacity={0.7}>
                                        <Image
                                            //@ts-ignore
                                            isLocal
                                            source={IMAGES.ic_trash}
                                            style={{
                                                width: Mixins.scale(20),
                                                height: Mixins.scale(20),
                                                marginRight: Mixins.scale(12),
                                                tintColor: Colors.RED_500
                                            }}
                                        />
                                        <MyText
                                            category="body.2"
                                            text="Thu hồi tin nhắn"
                                            style={{ color: Colors.RED_500 }}
                                        />
                                    </TouchableOpacity>
                                )} */}
                            </View>
                        </Pressable>
                    </Modal>

                    <View style={styles.otherUserContainer}>
                        <Image
                            //@ts-ignore
                            isLocal
                            source={{
                                uri: BASE_AVATAR_URL + item.creatorAvatar
                            }}
                            style={styles.avatar}
                        />
                        <PanGestureHandler
                            activeOffsetX={[-15, 15]} // Activate after moving 15px horizontally
                            activeOffsetY={[-50, 50]} // Require more vertical movement to activate
                            onGestureEvent={onGestureEvent}
                            onHandlerStateChange={onHandlerStateChange}
                            shouldCancelWhenOutside={true}
                            maxPointers={1} // Only allow one finger
                            avgTouches={false} // Don't average touches
                            enableTrackpadTwoFingerGesture={false}>
                            <Animated.View
                                ref={viewRef}
                                style={[
                                    styles.otherUserMessageContainer,
                                    { transform: [{ translateX }] },
                                    isHighlighted && styles.highlightedMessage,
                                    showMenu && styles.highlightedMessage
                                ]}>
                                <TouchableOpacity
                                    activeOpacity={1}
                                    disabled={item.status === 'DEACTIVE'}
                                    onLongPress={handleLongPress}
                                    style={{ flex: 1 }}>
                                    <Animated.View
                                        style={[
                                            styles.replyIconContainer,
                                            {
                                                left: Mixins.scale(-30),
                                                opacity: translateX.interpolate(
                                                    {
                                                        inputRange: [20, 100],
                                                        outputRange: [0, 1],
                                                        extrapolate: 'clamp'
                                                    }
                                                )
                                            }
                                        ]}>
                                        <Image
                                            //@ts-ignore
                                            isLocal
                                            source={IMAGES.ic_reply}
                                            style={styles.replyIcon}
                                        />
                                    </Animated.View>

                                    {item.fromComment && (
                                        <TouchableOpacity
                                            activeOpacity={0.8}
                                            onPress={() => {
                                                if (
                                                    onPressReplyPreview &&
                                                    item.fromComment
                                                ) {
                                                    onPressReplyPreview(
                                                        item.fromComment
                                                            .commentID
                                                    )
                                                }
                                            }}
                                            style={[
                                                styles.replyPreviewContainer,
                                                styles.currentUserReplyContainer
                                            ]}>
                                            <View
                                                style={[
                                                    styles.replyPreviewContent,
                                                    {
                                                        backgroundColor:
                                                            Colors.BG_NEUTRALS_SECONDARY
                                                    }
                                                ]}>
                                                <Image
                                                    //@ts-ignore
                                                    isLocal
                                                    source={{
                                                        uri:
                                                            BASE_AVATAR_URL +
                                                            item.fromComment
                                                                .creatorAvatar
                                                    }}
                                                    style={
                                                        styles.replyPreviewIcon
                                                    }
                                                />
                                                <View
                                                    style={
                                                        styles.replyPreviewContentContainer
                                                    }>
                                                    <MyText
                                                        category="bold.caption.1"
                                                        text={
                                                            item.fromComment
                                                                .creatorName
                                                        }
                                                        style={
                                                            styles.replyPreviewName
                                                        }
                                                    />
                                                    {item.fromComment.contentType === 'IMAGE' ? (
                                                        <View style={styles.replyImagePreviewContainer}>
                                                            <Image
                                                                //@ts-ignore
                                                                isLocal={false}
                                                                source={{ uri: JSON.parse(item.fromComment.content).url }}
                                                                style={styles.replyImageThumbnail}
                                                                resizeMode="cover"
                                                            />
                                                            <MyText
                                                                category="regular.caption.1"
                                                                text={'Hình ảnh'}
                                                                style={styles.replyPreviewText}
                                                                numberOfLines={1}
                                                            />
                                                        </View>
                                                    ) : ['PDF', 'EXCEL', 'WORD', 'POWERPOINT', 'OTHER_FILE'].includes(item.fromComment.contentType) ? (
                                                        <View style={styles.replyImagePreviewContainer}>
                                                            <Image
                                                                //@ts-ignore
                                                                isLocal
                                                                source={getFileIcon(item.fromComment.contentType)}
                                                                style={styles.replyImageThumbnail}
                                                                resizeMode="contain"
                                                            />
                                                            <MyText
                                                                category="regular.caption.1"
                                                                text={getTruncatedFileName(JSON.parse(item.fromComment.content).fileName)}
                                                                style={styles.replyPreviewText}
                                                                numberOfLines={1}
                                                            />
                                                        </View>
                                                    ) : (
                                                        <MyText
                                                            category="regular.caption.1"
                                                            text={item.fromComment.content}
                                                            style={styles.replyPreviewText}
                                                            numberOfLines={1}
                                                        />
                                                    )}
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    )}

                                    {renderMessageContent(item, false)}
                                    <MyText
                                        text={getTimeFromDateTime(item.createTime)}
                                        category="caption"
                                        style={styles.otherUserTimeText}
                                    />
                                </TouchableOpacity>
                            </Animated.View>
                        </PanGestureHandler>
                    </View>
                </>
            )
        }
    }
)

const LocalHighlightedText = ({
    text,
    searchText,
    style
}: {
    text: string
    searchText: string
    style?: any
}) => {
    if (!searchText || searchText.trim() === '') {
        return <MyText text={text} category="body.2" style={style} />
    }

    const parts = text.split(new RegExp(`(${searchText})`, 'gi'))

    return (
        <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            {parts.map((part, i) => {
                const isMatch = part.toLowerCase() === searchText.toLowerCase()
                return isMatch ? (
                    <View key={i} style={styles.highlightedText}>
                        <MyText text={part} category="body.2" style={style} />
                    </View>
                ) : (
                    <MyText
                        key={i}
                        text={part}
                        category="body.2"
                        style={style}
                    />
                )
            })}
        </View>
    )
}

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.7)', // Màu nền với độ trong suốt 70%
        position: 'relative'
    },
    modalPressable: {
        flex: 1,
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center'
    },
    focusedMessageOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'transparent',
        zIndex: 20
    },
    messageHighlightContainer: {
        position: 'absolute',
        width: '60%',
        zIndex: 30
    },
    currentUserHighlightPosition: {
        right: Mixins.scale(20),
        top: 0
    },
    otherUserHighlightPosition: {
        left: Mixins.scale(58),
        top: 0
    },
    focusedMessageContainer: {
        zIndex: 30,
        elevation: 20
    },
    modalContainer: {
        position: 'absolute',
        width: Mixins.scale(200),
        alignItems: 'flex-start',
        zIndex: 100,
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderRadius: Mixins.scale(8),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        overflow: 'hidden'
    },
    highlightedMessage: {
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderWidth: 2,
        borderColor: Colors.BORDER_ACTIVE,
        zIndex: 10,
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.4,
        shadowRadius: 4,
        opacity: 1
    },
    highlightedMessageCurrent: {
        backgroundColor: Colors.BRAND_950,
        borderWidth: 2,
        borderColor: Colors.YELLOW_500,
        zIndex: 10,
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.4,
        shadowRadius: 4,
        opacity: 1
    },
    highlightedText: {
        backgroundColor: Colors.YELLOW_800
    },
    replyIconContainer: {
        position: 'absolute',
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderRadius: Mixins.scale(12),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1.5,
        elevation: 2,
        zIndex: 10
    },
    replyIcon: {
        width: Mixins.scale(16),
        height: Mixins.scale(16),
        tintColor: Colors.BG_SOLID_BRAND
    },
    replyPreviewContainer: {
        flexDirection: 'row',
        marginBottom: Mixins.scale(6),
        width: '100%',
        borderRadius: Mixins.scale(4)
    },
    currentUserReplyContainer: {
        backgroundColor: 'rgba(255,255,255,0.1)'
    },
    otherUserReplyContainer: {
        backgroundColor: 'rgba(0,0,0,0.03)'
    },
    replyPreviewContent: {
        flex: 1,
        flexDirection: 'row',
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        paddingVertical: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(16),
        borderRadius: Mixins.scale(12),
        borderTopLeftRadius: Mixins.scale(4),
        borderWidth: 1,
        borderColor: Colors.BORDER_PRIMARY,
        gap: Mixins.scale(6)
    },
    replyPreviewIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        borderRadius: Mixins.scale(12)
    },
    replyPreviewContentContainer: {
        flex: 1,
        gap: Mixins.scale(6)
    },
    replyPreviewName: {
        color: Colors.TEXT_PRIMARY
    },
    replyPreviewText: {
        color: Colors.TEXT_PRIMARY,
        flex: 1,
        flexShrink: 1
    },
    currentUserContainer: {
        gap: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'flex-end'
    },
    otherUserContainer: {
        gap: Mixins.scale(16),
        flexDirection: 'row'
    },
    avatar: {
        width: Mixins.scale(32),
        height: Mixins.scale(32),
        borderRadius: Mixins.scale(16)
    },
    currentUserMessageContainer: {
        width: '60%',
        marginLeft: Mixins.scale(58),
        paddingVertical: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(16),
        backgroundColor: Colors.BRAND_950,
        borderWidth: 1,
        borderColor: Colors.BRAND_900,
        borderRadius: Mixins.scale(12),
        gap: Mixins.scale(2)
    },
    otherUserMessageContainer: {
        width: '60%',
        marginRight: Mixins.scale(58),
        paddingVertical: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(16),
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderWidth: 1,
        borderColor: Colors.BORDER_PRIMARY,
        borderRadius: Mixins.scale(12),
        borderTopLeftRadius: Mixins.scale(4),
        gap: Mixins.scale(2)
    },
    currentUserMessageText: {
        color: Colors.TEXT_PRIMARY
    },
    currentUserTimeText: {
        color: Colors.TEXT_DISABLE
    },
    otherUserMessageText: {
        color: Colors.TEXT_PRIMARY
    },
    otherUserTimeText: {
        color: Colors.TEXT_DISABLE
    },
    messageImage: {
        width: '100%',
        height: Mixins.scale(200),
        borderRadius: Mixins.scale(8),
        marginBottom: Mixins.scale(4)
    },
    replyImagePreviewContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(8),
        maxWidth: '100%',
        flexWrap: 'nowrap'
    },
    replyImageThumbnail: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        borderRadius: Mixins.scale(4),
        flexShrink: 0
    }
})

export default ChatItem
