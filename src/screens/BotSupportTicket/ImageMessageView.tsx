import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Modal, Pressable, Platform, Dimensions, StatusBar } from 'react-native';
import { Colors, Mixins, MyText } from '@react-native-xwork';
import { Image } from '@mwg-kits/components';
import IMAGES from '../../assets/images';
import { getTimeFromDateTime } from '../../common/helper/dateTimeHelper';
import { useSelector } from 'react-redux';
import { RootReducerType } from '../../store/reducer';

interface ImageMessageViewProps {
    content: string;
    isCurrentUser: boolean;
}

const ImageMessageView = ({ content, isCurrentUser }: ImageMessageViewProps) => {
    const [modalVisible, setModalVisible] = useState(false);

    let imageData;
    try {
        imageData = JSON.parse(content);
    } catch (error) {
        console.error('Error parsing image data:', error);
        return (
            <View style={styles.errorContainer}>
                <MyText
                    text="Không thể hiển thị hình ảnh"
                    category="body.2"
                    style={isCurrentUser ? styles.currentUserText : styles.otherUserText}
                />
            </View>
        );
    }

    const { url, fileName } = imageData;

    const handleOpenImage = () => {
        setModalVisible(true);
    };

    // Ẩn thanh trạng thái khi mở modal hình ảnh
    useEffect(() => {
        if (modalVisible) {
            StatusBar.setHidden(true);
        } else {
            StatusBar.setHidden(false);
        }
    }, [modalVisible]);

    return (
        <>
            <TouchableOpacity
                style={[
                    styles.container,
                    isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer
                ]}
                onPress={handleOpenImage}
                activeOpacity={0.7}
            >
                <View style={styles.messageContainer}>
                    <Image
                        //@ts-ignore
                        isLocal={false}
                        source={{ uri: url }}
                        style={styles.messageImage}
                        resizeMode="cover"
                    />
                    <MyText
                        text={getTimeFromDateTime(fileName) || ""}
                        category="caption"
                        style={isCurrentUser ? styles.currentUserTimeText : styles.otherUserTimeText}
                    />
                </View>
            </TouchableOpacity>

            {/* Image Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setModalVisible(false)}
                statusBarTranslucent={true}
                presentationStyle="overFullScreen"
            >
                <Pressable
                    style={styles.modalOverlay}
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.imageModalContainer}>
                        <View style={styles.imageModalHeader}>
                            <TouchableOpacity
                                onPress={() => setModalVisible(false)}
                                style={styles.closeButton}
                            >
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_close}
                                    style={styles.closeIcon}
                                />
                            </TouchableOpacity>
                        </View>
                        <View style={styles.imageViewerContainer}>
                            <Image
                                //@ts-ignore
                                isLocal={false}
                                source={{ uri: url }}
                                style={styles.fullImage}
                                resizeMode="contain"
                            />
                        </View>
                    </View>
                </Pressable>
            </Modal>
        </>
    );
};

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const styles = StyleSheet.create({
    messageImage: {
        width: '100%',
        height: Mixins.scale(200),
        borderRadius: Mixins.scale(8),
        marginBottom: Mixins.scale(4)
    },
    container: {
        width: '100%',
        marginBottom: Mixins.scale(4),
    },
    currentUserContainer: {
        alignItems: 'flex-end',
    },
    otherUserContainer: {
        alignItems: 'flex-start',
    },
    messageContainer: {
        overflow: 'hidden',
        width: '100%',
    },
    timeText: {
        marginTop: Mixins.scale(4),
        alignSelf: 'flex-end',
    },
    currentUserTimeText: {
        color: Colors.TEXT_DISABLE,
    },
    otherUserTimeText: {
        color: Colors.TEXT_DISABLE,
    },
    errorContainer: {
        padding: Mixins.scale(16),
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        borderRadius: Mixins.scale(8),
        marginBottom: Mixins.scale(4)
    },
    currentUserText: {
        color: Colors.TEXT_PRIMARY
    },
    otherUserText: {
        color: Colors.TEXT_PRIMARY
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageModalContainer: {
        width: SCREEN_WIDTH,
        height: SCREEN_HEIGHT,
        backgroundColor: 'black',
    },
    imageModalHeader: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? Mixins.scale(40) : Mixins.scale(50),
        right: 0,
        left: 0,
        zIndex: 10,
        height: Mixins.scale(50),
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        paddingHorizontal: Mixins.scale(16),
    },
    closeButton: {
        padding: Mixins.scale(8),
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        borderRadius: Mixins.scale(20),
    },
    closeIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        tintColor: 'white',
    },
    imageViewerContainer: {
        flex: 1,
        backgroundColor: '#000',
        justifyContent: 'center',
        alignItems: 'center',
    },
    fullImage: {
        width: SCREEN_WIDTH,
        height: SCREEN_HEIGHT,
        backgroundColor: '#000',
    }
});

export default ImageMessageView;
