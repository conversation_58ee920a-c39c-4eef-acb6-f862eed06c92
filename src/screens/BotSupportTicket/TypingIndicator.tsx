import React, { useEffect, useRef } from 'react'
import { View, StyleSheet, Animated } from 'react-native'
import { Colors, Mixins } from '@react-native-xwork'

interface TypingIndicatorProps {
    isVisible: boolean
}

const TypingIndicator = ({ isVisible }: TypingIndicatorProps) => {
    // Refs cho các animation dots
    const dot1Opacity = useRef(new Animated.Value(0)).current
    const dot2Opacity = useRef(new Animated.Value(0)).current
    const dot3Opacity = useRef(new Animated.Value(0)).current

    // Animation sequence
    const startAnimation = () => {
        // Reset các giá trị
        dot1Opacity.setValue(0)
        dot2Opacity.setValue(0)
        dot3Opacity.setValue(0)

        // Tạo animation sequence
        Animated.sequence([
            // Dot 1 hiện lên
            Animated.timing(dot1Opacity, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true
            }),
            // Dot 2 hiện lên
            Animated.timing(dot2Opacity, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true
            }),
            // Dot 3 hiện lên
            Animated.timing(dot3Opacity, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true
            }),
            // Tạm dừng một chút
            Animated.delay(300),
            // Tất cả dots biến mất
            Animated.parallel([
                Animated.timing(dot1Opacity, {
                    toValue: 0,
                    duration: 200,
                    useNativeDriver: true
                }),
                Animated.timing(dot2Opacity, {
                    toValue: 0,
                    duration: 200,
                    useNativeDriver: true
                }),
                Animated.timing(dot3Opacity, {
                    toValue: 0,
                    duration: 200,
                    useNativeDriver: true
                })
            ])
        ]).start((result) => {
            // Lặp lại animation nếu vẫn đang hiển thị
            if (result.finished && isVisible) {
                startAnimation()
            }
        })
    }

    // Bắt đầu hoặc dừng animation khi isVisible thay đổi
    useEffect(() => {
        if (isVisible) {
            startAnimation()
        }
    }, [isVisible])

    // Không render gì nếu không hiển thị
    if (!isVisible) {
        return null
    }

    return (
        <View style={styles.container}>
            <View style={styles.typingContainer}>
                <Animated.View
                    style={[
                        styles.dot,
                        {
                            opacity: dot1Opacity
                        }
                    ]}
                />
                <Animated.View
                    style={[
                        styles.dot,
                        {
                            opacity: dot2Opacity
                        }
                    ]}
                />
                <Animated.View
                    style={[
                        styles.dot,
                        {
                            opacity: dot3Opacity
                        }
                    ]}
                />
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        marginLeft: Mixins.scale(16),
        zIndex: 10
    },
    typingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.BUTTON_SOLID_DESTRUCTIVE_DIVIDER,
        borderRadius: Mixins.scale(10),
        paddingHorizontal: Mixins.scale(8),
        paddingVertical: Mixins.scale(8),
        alignSelf: 'flex-start'
    },
    dot: {
        width: Mixins.scale(5),
        height: Mixins.scale(5),
        borderRadius: Mixins.scale(2.5),
        backgroundColor: Colors.TEXT_PRIMARY,
        marginHorizontal: Mixins.scale(2)
    }
})

export default TypingIndicator
