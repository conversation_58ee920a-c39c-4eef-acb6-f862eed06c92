import React from 'react'
import { View, StyleSheet, TouchableOpacity } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import IMAGES from '../../assets/images'

interface AttachmentOptionsProps {
    onSelectFile: () => void
    onSelectMediaGallery?: () => void
}

const AttachmentOptions = ({
    onSelectFile,
    onSelectMediaGallery
}: AttachmentOptionsProps) => {
    return (
        <View style={styles.container}>
            <TouchableOpacity
                style={styles.optionItem}
                onPress={onSelectFile}
                activeOpacity={0.7}
            >
                <View style={styles.iconContainer}>
                    <Image
                        //@ts-ignore
                        isLocal
                        source={IMAGES.ic_file}
                        style={styles.optionIcon}
                    />
                </View>
                <MyText
                    category="body.2"
                    text="File"
                    style={styles.optionText}
                />
            </TouchableOpacity>

            <TouchableOpacity
                style={styles.optionItem}
                onPress={onSelectMediaGallery}
                activeOpacity={0.7}
            >
                <View style={[styles.iconContainer, styles.mediaGalleryIconContainer]}>
                    <Image
                        //@ts-ignore
                        isLocal
                        source={IMAGES.ic_image}
                        style={styles.optionIcon}
                    />
                </View>
                <MyText
                    category="body.2"
                    text="Hình ảnh"
                    style={styles.optionText}
                />
            </TouchableOpacity>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        paddingVertical: Mixins.scale(16),
        backgroundColor: Colors.WHITE,
        borderBottomLeftRadius: Mixins.scale(16),
        borderBottomRightRadius: Mixins.scale(16),
    },
    optionItem: {
        alignItems: 'center',
        width: Mixins.scale(60)
    },
    iconContainer: {
        width: Mixins.scale(44),
        height: Mixins.scale(44),
        borderRadius: Mixins.scale(22),
        backgroundColor: Colors.BG_SOFT_BRAND,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: Mixins.scale(8)
    },
    mediaGalleryIconContainer: {
        backgroundColor: Colors.BG_SOFT_SUCCESS
    },
    optionIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24)
    },
    optionText: {
        color: Colors.TEXT_PRIMARY,
        textAlign: 'center'
    }
})

export default AttachmentOptions
