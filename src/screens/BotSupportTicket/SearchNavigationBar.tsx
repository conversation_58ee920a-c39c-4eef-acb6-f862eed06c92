import React from 'react'
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import { IMAGES } from '../../assets'

interface SearchNavigationBarProps {
    currentIndex: number
    totalResults: number
    searchText: string
    onPrevResult: () => void
    onNextResult: () => void
}

const SearchNavigationBar = ({
    currentIndex,
    totalResults,
    searchText,
    onPrevResult,
    onNextResult
}: SearchNavigationBarProps) => {
    if (totalResults === 0 && searchText.trim() !== '')
        return (
            <View style={styles.container}>
                <MyText
                    category="body.2"
                    text="Không tìm thấy kết quả"
                    style={styles.resultCountText}
                />
            </View>
        )

    return (
        <View style={styles.container}>
            <View style={styles.resultCountContainer}>
                <MyText
                    category="body.2"
                    text={`${
                        searchText.trim() === '' && totalResults === 0
                            ? '0/0'
                            : currentIndex + 1 + '/' + totalResults
                    }`}
                    style={styles.resultCountText}
                />
            </View>
            <View style={styles.navigationContainer}>
                <TouchableOpacity
                    style={[styles.navigationButton]}
                    onPress={onNextResult}
                    disabled={
                        currentIndex === totalResults - 1 ||
                        searchText.trim() === ''
                    }>
                    <Image
                        //@ts-ignore
                        isLocal
                        source={IMAGES.ic_chevron_up}
                        style={[
                            styles.navigationIcon,
                            (currentIndex === totalResults - 1 ||
                                searchText.trim() === '') &&
                                styles.disabledButton
                        ]}
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.navigationButton]}
                    onPress={onPrevResult}
                    disabled={currentIndex === 0 || searchText.trim() === ''}>
                    <Image
                        //@ts-ignore
                        isLocal
                        source={IMAGES.ic_chevron_down}
                        style={[
                            styles.navigationIcon,
                            (currentIndex === 0 || searchText.trim() === '') &&
                                styles.disabledButton
                        ]}
                    />
                </TouchableOpacity>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(12),
        borderTopWidth: 1,
        borderTopColor: Colors.BORDER_PRIMARY
    },
    resultCountContainer: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    resultCountText: {
        color: Colors.TEXT_PRIMARY,
        fontWeight: '500'
    },
    navigationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(8)
    },
    navigationButton: {
        width: Mixins.scale(36),
        height: Mixins.scale(36),
        alignItems: 'center',
        justifyContent: 'center'
    },
    disabledButton: {
        tintColor: Colors.ICON_NEUTRALS_DISABLE
    },
    navigationIcon: {
        width: Mixins.scale(20),
        height: Mixins.scale(20),
        tintColor: Colors.ICON_NEUTRALS_PRIMARY
    }
})

export default SearchNavigationBar
