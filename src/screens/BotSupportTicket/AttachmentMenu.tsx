import React from 'react'
import { View, StyleSheet, TouchableOpacity, Modal, Pressable } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import IMAGES from '../../assets/images'

interface AttachmentMenuProps {
    visible: boolean
    onClose: () => void
    onSelectFile: () => void
    onSelectMediaGallery?: () => void
}

const AttachmentMenu = ({
    visible,
    onClose,
    onSelectFile,
    onSelectMediaGallery
}: AttachmentMenuProps) => {
    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="fade"
            onRequestClose={onClose}
        >
            <Pressable style={styles.modalOverlay} onPress={onClose}>
                <View style={styles.menuContainer}>
                    <View style={styles.optionsContainer}>
                        <TouchableOpacity
                            style={styles.optionItem}
                            onPress={onSelectFile}
                            activeOpacity={0.7}
                        >
                            <View style={styles.iconContainer}>
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_file}
                                    style={styles.optionIcon}
                                />
                            </View>
                            <MyText
                                category="body.2"
                                text="File"
                                style={styles.optionText}
                            />
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.optionItem}
                            onPress={onSelectMediaGallery}
                            activeOpacity={0.7}
                        >
                            <View style={[styles.iconContainer, styles.mediaGalleryIconContainer]}>
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_image}
                                    style={styles.optionIcon}
                                />
                            </View>
                            <MyText
                                category="body.2"
                                text="Ảnh & Video"
                                style={styles.optionText}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </Pressable>
        </Modal>
    )
}

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end'
    },
    menuContainer: {
        backgroundColor: Colors.WHITE,
        borderTopLeftRadius: Mixins.scale(16),
        borderTopRightRadius: Mixins.scale(16),
        paddingBottom: Mixins.scale(34) // Thêm padding cho thanh home indicator
    },
    optionsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: Mixins.scale(24),
        paddingHorizontal: Mixins.scale(16)
    },
    optionItem: {
        alignItems: 'center',
        width: Mixins.scale(100)
    },
    iconContainer: {
        width: Mixins.scale(44),
        height: Mixins.scale(44),
        borderRadius: Mixins.scale(22),
        backgroundColor: Colors.BG_SOFT_BRAND,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: Mixins.scale(8)
    },
    mediaGalleryIconContainer: {
        backgroundColor: Colors.BG_SOFT_WARNING
    },
    optionIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24)
    },
    optionText: {
        color: Colors.TEXT_PRIMARY,
        textAlign: 'center'
    }
})

export default AttachmentMenu
