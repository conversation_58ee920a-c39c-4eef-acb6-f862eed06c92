import React, { useState, useCallback } from 'react'
import { View, StyleSheet, TouchableOpacity } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'

interface TruncatedTextProps {
    text: string
    style?: any
    maxLength?: number
    readMoreText?: string
    readMoreStyle?: any
}

/**
 * Component hiển thị văn bản dài với chức năng "xem thêm"
 * @param text Nội dung văn bản cần hiển thị
 * @param style Style cho văn bản
 * @param maxLength Độ dài tối đa trước khi cắt ngắn (mặc định: 300 ký tự)
 * @param readMoreText Văn bản cho nút "xem thêm" (mặc định: "... xem thêm")
 * @param readMoreStyle Style cho nút "xem thêm"
 */
const TruncatedText = ({
    text,
    style,
    maxLength = 300,
    readMoreText = '... xem thêm',
    readMoreStyle
}: TruncatedTextProps) => {
    const [isExpanded, setIsExpanded] = useState(false)
    const needsTruncation = text.length > maxLength

    const toggleExpanded = useCallback(() => {
        setIsExpanded(!isExpanded)
    }, [isExpanded])

    if (!needsTruncation || isExpanded) {
        return (
            <View>
                <MyText text={text} category="body.2" style={style} />
                {needsTruncation && isExpanded && (
                    <TouchableOpacity onPress={toggleExpanded} activeOpacity={0.7}>
                        <MyText
                            text="Thu gọn"
                            category="body.2"
                            style={[styles.readMoreText, readMoreStyle]}
                        />
                    </TouchableOpacity>
                )}
            </View>
        )
    }

    const truncatedText = text.substring(0, maxLength)

    return (
        <View>
            <MyText text={truncatedText} category="body.2" style={style} />
            <TouchableOpacity onPress={toggleExpanded} activeOpacity={0.7}>
                <MyText
                    text={readMoreText}
                    category="body.2"
                    style={[styles.readMoreText, readMoreStyle]}
                />
            </TouchableOpacity>
        </View>
    )
}

const styles = StyleSheet.create({
    readMoreText: {
        color: '#0066FF',
        marginTop: Mixins.scale(4)
    }
})

export default TruncatedText
