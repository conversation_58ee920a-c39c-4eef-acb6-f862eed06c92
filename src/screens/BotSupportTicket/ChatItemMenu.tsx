import React from 'react'
import { View, StyleSheet, TouchableOpacity, Dimensions } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import IMAGES from '../../assets/images'

interface ChatItemMenuProps {
    onReply: () => void
    onCopy: () => void
    onDelete: () => void
    position: { x: number; y: number; width: number; height: number }
    isCurrentUser: boolean
}

// Tính toán vị trí an toàn cho menu
const calculateMenuPosition = (position: {
    x: number
    y: number
    width: number
    height: number
}) => {
    const screenHeight = Dimensions.get('window').height
    const screenWidth = Dimensions.get('window').width
    const menuHeight = Mixins.scale(150)
    const menuWidth = Mixins.scale(200)
    const safeAreaBottom = screenHeight - Mixins.scale(50)

    // Luôn hiển thị menu bên dưới tin nhắn (giống Messenger)
    const defaultPositionY = position.y + position.height + Mixins.scale(12)

    // Kiểm tra xem menu có vượt quá safe area không
    const needsAdjustment = defaultPositionY + menuHeight > safeAreaBottom

    // Nếu menu sẽ vượt quá safe area, vẫn hiển thị bên dưới nhưng đẩy tin nhắn lên trên
    // Việc đẩy tin nhắn lên trên sẽ được xử lý trong ChatItem.tsx
    const positionY = defaultPositionY

    // Tính toán vị trí X
    // Trung tâm menu với tin nhắn
    let positionX = position.x + position.width / 2 - menuWidth / 2

    // Đảm bảo menu không vượt quá màn hình bên trái
    if (positionX < Mixins.scale(16)) {
        positionX = Mixins.scale(16)
    }

    // Đảm bảo menu không vượt quá màn hình bên phải
    if (positionX + menuWidth > screenWidth - Mixins.scale(16)) {
        positionX = screenWidth - menuWidth - Mixins.scale(16)
    }

    return {
        top: positionY,
        left: positionX,
        needsAdjustment,
        menuHeight
    }
}

const ChatItemMenu = ({
    onReply,
    onCopy,
    onDelete,
    position,
    isCurrentUser
}: ChatItemMenuProps) => {
    return (
        <View style={[styles.container, calculateMenuPosition(position)]}>
            <TouchableOpacity
                style={styles.menuItem}
                onPress={onReply}
                activeOpacity={0.7}>
                <Image
                    //@ts-ignore
                    isLocal
                    source={IMAGES.ic_reply}
                    style={styles.menuIcon}
                />
                <MyText
                    category="body.2"
                    text="Trả lời"
                    style={styles.menuText}
                />
            </TouchableOpacity>

            <TouchableOpacity
                style={styles.menuItem}
                onPress={onCopy}
                activeOpacity={0.7}>
                <Image
                    //@ts-ignore
                    isLocal
                    source={IMAGES.ic_copy}
                    style={styles.menuIcon}
                />
                <MyText
                    category="body.2"
                    text="Copy tin nhắn"
                    style={styles.menuText}
                />
            </TouchableOpacity>

            {isCurrentUser && (
                <TouchableOpacity
                    style={styles.menuItem}
                    onPress={onDelete}
                    activeOpacity={0.7}>
                    <Image
                        //@ts-ignore
                        isLocal
                        source={IMAGES.ic_trash}
                        style={[styles.menuIcon, styles.deleteIcon]}
                    />
                    <MyText
                        category="body.2"
                        text="Thu hồi tin nhắn"
                        style={styles.deleteText}
                    />
                </TouchableOpacity>
            )}
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderRadius: Mixins.scale(8),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: Mixins.scale(200),
        zIndex: 1000
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: Mixins.scale(12),
        paddingHorizontal: Mixins.scale(16)
    },
    menuIcon: {
        width: Mixins.scale(20),
        height: Mixins.scale(20),
        marginRight: Mixins.scale(12),
        tintColor: Colors.TEXT_PRIMARY
    },
    menuText: {
        color: Colors.TEXT_PRIMARY
    },
    deleteIcon: {
        tintColor: Colors.RED_500
    },
    deleteText: {
        color: Colors.RED_500
    }
})

export default ChatItemMenu
