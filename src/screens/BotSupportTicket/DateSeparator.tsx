import React, { memo } from 'react'
import { View, StyleSheet } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'

interface DateSeparatorProps {
    date: string
    isVisible: boolean
}

const DateSeparator = memo(({ date, isVisible }: DateSeparatorProps) => {
    if (!isVisible) return null

    return (
        <View style={styles.container}>
            <View style={styles.dateContainer}>
                <MyText
                    text={date}
                    category="bold.caption"
                    style={styles.dateText}
                />
            </View>
        </View>
    )
})

const styles = StyleSheet.create({
    container: {
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: Mixins.scale(8),
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 100
    },
    dateContainer: {
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        paddingHorizontal: Mixins.scale(12),
        paddingVertical: Mixins.scale(4),
        borderRadius: Mixins.scale(16),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1.5,
        elevation: 2
    },
    dateText: {
        color: Colors.TEXT_PRIMARY
    }
})

export default DateSeparator
