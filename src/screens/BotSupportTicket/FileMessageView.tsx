import React from 'react';
import { View, StyleSheet, TouchableOpacity, Linking, Platform } from 'react-native';
import { Colors, Mixins, MyText } from '@react-native-xwork';
import { Image } from '@mwg-kits/components';
import IMAGES from '../../assets/images';

// Hàm cắt ngắn tên file nếu quá dài
const getTruncatedFileName = (fileName: string) => {
    if (!fileName) return '';
    if (fileName.length <= 20) return fileName;

    // Lấy phần mở rộng của file
    const lastDotIndex = fileName.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : '';

    // Cắt ngắn tên file và giữ lại phần mở rộng
    const nameWithoutExtension = lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
    return nameWithoutExtension.substring(0, 16) + '...' + extension;
};

// Hàm lấy icon dựa vào loại file
const getFileIcon = (contentType: string) => {
    switch (contentType) {
        case 'PDF':
            return IMAGES.ic_file_pdf;
        case 'EXCEL':
            return IMAGES.ic_file_excel;
        case 'WORD':
            return IMAGES.ic_file_word;
        case 'POWERPOINT':
            return IMAGES.ic_file_powerpoint;
        default:
            return IMAGES.ic_file_generic;
    }
};

interface FileMessageViewProps {
    content: string;
    contentType: string;
    isCurrentUser: boolean;
}

const FileMessageView = ({ content, contentType, isCurrentUser }: FileMessageViewProps) => {
    let fileData;
    try {
        fileData = JSON.parse(content);
    } catch (error) {
        console.error('Error parsing file data:', error);
        return (
            <View style={styles.errorContainer}>
                <MyText 
                    text="Không thể hiển thị file" 
                    category="body.2" 
                    style={isCurrentUser ? styles.currentUserText : styles.otherUserText} 
                />
            </View>
        );
    }

    const { fileName, downloadUrl } = fileData;

    const handleDownload = async () => {
        try {
            // Mở URL để tải file
            await Linking.openURL(downloadUrl);
        } catch (error) {
            console.error('Error opening download URL:', error);
        }
    };

    return (
        <TouchableOpacity 
            style={[
                styles.container, 
                isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer
            ]} 
            onPress={handleDownload}
            activeOpacity={0.7}
        >
            <View style={styles.iconContainer}>
                <Image
                    //@ts-ignore
                    isLocal
                    source={getFileIcon(contentType)}
                    style={styles.fileIcon}
                    resizeMode="contain"
                />
            </View>
            <View style={styles.fileInfoContainer}>
                <MyText 
                    text={getTruncatedFileName(fileName)} 
                    category="body.2" 
                    style={isCurrentUser ? styles.currentUserText : styles.otherUserText}
                    numberOfLines={1}
                />
                <MyText 
                    text="Nhấn để tải xuống" 
                    category="caption" 
                    style={styles.downloadText}
                />
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: Mixins.scale(8),
        borderRadius: Mixins.scale(8),
        marginBottom: Mixins.scale(4),
        borderWidth: 1,
    },
    currentUserContainer: {
        backgroundColor: Colors.BRAND_900,
        borderColor: Colors.BRAND_800,
    },
    otherUserContainer: {
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        borderColor: Colors.BORDER_PRIMARY,
    },
    iconContainer: {
        width: Mixins.scale(40),
        height: Mixins.scale(40),
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: Mixins.scale(8),
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        borderRadius: Mixins.scale(4),
    },
    fileIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
    },
    fileInfoContainer: {
        flex: 1,
        justifyContent: 'center',
    },
    currentUserText: {
        color: Colors.TEXT_PRIMARY,
    },
    otherUserText: {
        color: Colors.TEXT_PRIMARY,
    },
    downloadText: {
        color: Colors.TEXT_DISABLE,
        marginTop: Mixins.scale(2),
    },
    errorContainer: {
        padding: Mixins.scale(8),
        borderRadius: Mixins.scale(8),
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        borderWidth: 1,
        borderColor: Colors.BADGE_OUTLINE_RED_TEXT,
    }
});

export default FileMessageView;
