import React, { useMemo } from 'react'
import { View, StyleSheet, Text } from 'react-native'
import { Colors, Mixins, MyText } from '@react-native-xwork'

interface HighlightedTextProps {
    text: string
    searchText: string
    style?: any
    maxLength?: number
}

/**
 * Component hiển thị văn bản với từ khóa tìm kiếm được highlight
 * @param text Nội dung văn bản cần hiển thị
 * @param searchText Từ khóa tìm kiếm cần highlight
 * @param style Style cho văn bản
 * @param maxLength Độ dài tối đa trước khi cắt ngắn (mặc định: không giới hạn)
 */
const HighlightedText = ({
    text,
    searchText,
    style,
    maxLength
}: HighlightedTextProps) => {
    const parts = useMemo(() => {
        if (!searchText.trim()) {
            return [{ text, highlight: false }]
        }

        const regex = new RegExp(`(${searchText})`, 'gi')
        const parts = text.split(regex)

        return parts.map((part, i) => {
            const isMatch = part.toLowerCase() === searchText.toLowerCase()
            return { text: part, highlight: isMatch }
        })
    }, [text, searchText])

    // Nếu có maxLength, cắt ngắn văn bản
    const displayText = useMemo(() => {
        if (maxLength && text.length > maxLength) {
            return text.substring(0, maxLength) + '...'
        }
        return text
    }, [text, maxLength])

    // Nếu không có từ khóa tìm kiếm, hiển thị văn bản thông thường
    if (!searchText.trim()) {
        return <MyText text={displayText} category="body.2" style={style} />
    }

    return (
        <Text style={[styles.baseText, style]}>
            {parts.map((part, i) => (
                <Text
                    key={i}
                    style={part.highlight ? styles.highlightedText : {}}
                >
                    {part.text}
                </Text>
            ))}
        </Text>
    )
}

const styles = StyleSheet.create({
    baseText: {
        fontSize: Mixins.scaleFont(14),
        lineHeight: Mixins.scaleFont(20),
        color: Colors.TEXT_PRIMARY,
        fontFamily: 'SF Pro Text'
    },
    highlightedText: {
        backgroundColor: Colors.YELLOW_300
    }
})

export default HighlightedText
